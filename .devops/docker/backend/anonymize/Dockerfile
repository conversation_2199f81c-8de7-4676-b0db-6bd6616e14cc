FROM python:3.11-bookworm

ENV PYTHONUNBUFFERED=1 \
  POETRY_VERSION=2.1.2

RUN apt-get update && apt-get install -y lsb-release && apt-get clean all
RUN sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
RUN wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add -

RUN apt-get update -qq \
  && apt-get install -qq -y \
    gcc \
    gettext \
    vim \
    postgresql-client-14 \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean -qq -y \
  && pip install --upgrade pip \
  && pip install "poetry==${POETRY_VERSION}"

RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" \
  && unzip awscliv2.zip \
  && ./aws/install

ENV VIRTUAL_ENV=/opt/venv
RUN python3 -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

COPY ./src/poetry.lock ./src/pyproject.toml ./

RUN --mount=type=cache,mode=0777,target=/root/.cache/pip poetry install --no-ansi

ENV HOME=/home/<USER>

WORKDIR /app

ARG USER_ID=1000

ENV USER_ID=${USER_ID}

RUN addgroup --system --gid ${USER_ID} django \
  && adduser --system --home ${HOME} --uid ${USER_ID} --ingroup django django

RUN chown -R django /app /home/<USER>
COPY --chown=django:django ./src /app
COPY --chown=django:django ./aws /home/<USER>/.aws
RUN mkdir -p /app/media && chown django:django /app/media
RUN mkdir -p /app/errors && chown django:django /app/errors
RUN mkdir -p /app/shared && chown django:django /app/shared

USER django

CMD ["bash", "-c", "./anonymize_and_minify_db.sh 2> >(tee -a ./errors/$(date +'%F'.log)) || (c=$?; ./anonymize_slack_notification.sh; (exit $c))"]
