FROM python:3.11-bookworm AS development_build

ARG DJANGO_ENV
ARG USER_ID=1000

ENV USER_ID=${USER_ID} \
  HOME=/home/<USER>
  DJANGO_ENV=${DJANGO_ENV} \
  PYTHONUNBUFFERED=1 \
  POETRY_CACHE_DIR=/var/cache/pypoetry \
  POETRY_VERSION=2.1.2

RUN apt-get update -qq \
  && apt-get install -qq -y \
    gcc \
    gettext \
    vim \
    xvfb \
    librsvg2-bin \
    # latex and pdflatex
    texlive-latex-recommended \
    texlive-latex-extra \
    texlive-lang-polish \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean -qq -y \
  && pip install --upgrade pip \
  && pip install "poetry==${POETRY_VERSION}"


# create the user and group to run the Django application as
RUN addgroup --system --gid ${USER_ID} django \
  && adduser --system --home ${HOME} --uid ${USER_ID} --ingroup django django

COPY ./.devops/docker/backend/entrypoint.sh /entrypoint.sh
RUN sed -i 's/\r//' /entrypoint.sh && chmod +x /entrypoint.sh

COPY ./.devops/docker/backend/celery/worker/start.sh /start-celeryworker.sh
RUN sed -i 's/\r//' /start-celeryworker.sh \
    && chmod +x /start-celeryworker.sh

COPY ./.devops/docker/backend/celery/beat/start.sh /start-celerybeat.sh
RUN sed -i 's/\r//' /start-celerybeat.sh \
    && chmod +x /start-celerybeat.sh

COPY ./.devops/docker/backend/celery/flower/start.sh /start-flower.sh
RUN sed -i 's/\r//' /start-flower.sh \
    && chmod +x /start-flower.sh

COPY ./.devops/docker/backend/start.sh /start.sh
RUN sed -i 's/\r//' /start.sh && chmod +x /start.sh

ENTRYPOINT ["/entrypoint.sh"]
CMD ["/start.sh"]


WORKDIR /app

ENV VIRTUAL_ENV=/opt/venv
RUN python3 -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

COPY ./src/poetry.lock ./src/pyproject.toml /app/

RUN poetry install \
    $(if [ "$DJANGO_ENV" = 'production' ]; then echo '--no-dev'; fi) \
    --no-interaction \
    --no-ansi \
  # Cleaning poetry installation's cache for production:
  && if [ "$DJANGO_ENV" = 'production' ]; then rm -rf "$POETRY_CACHE_DIR"; fi
RUN chown -R django /opt/venv /var/cache/pypoetry

# create font directory and copy the font
RUN mkdir -p /home/<USER>/.fonts
COPY ./src/producers/gh/latex/cstm_instruction/webfonts/ /home/<USER>/.fonts
# refresh system font cache
RUN fc-cache -f

RUN chown -R django /app /home/<USER>
USER django

# PRODUCTION ONLY STAGE
FROM development_build as production_build

USER root
COPY --chown=django:django ./src /app
COPY --chown=django:django ./shared/ /app/shared/

USER django
