FROM node:23.11-bookworm AS development_build

ARG ENVIRONMENT
ARG NODE_ENV
ARG GITHUB_TOKEN

ENV DOCKER_BUILDKIT=1
ENV CHOKIDAR_USEPOLLING=true
ENV ENVIRONMENT=${ENVIRONMENT}
ENV NODE_ENV=${NODE_ENV}
ENV GITHUB_TOKEN=${GITHUB_TOKEN}

WORKDIR /nuxt3

COPY ./nuxt3/package.json /nuxt3/
COPY ./nuxt3/package-lock.json /nuxt3/

SHELL ["/bin/bash", "--login", "-c"]

RUN printf "@tylkocom:registry=https://npm.pkg.github.com/\n//npm.pkg.github.com/:_authToken=$GITHUB_TOKEN" > .npmrc
RUN npm install
RUN npm install -g @sanity/cli

COPY ./nuxt3 /nuxt3/

EXPOSE 3000

CMD ["npm", "run", "dev"]

FROM development_build as production_build

ENV NITRO_PRESET=node_cluster

ARG SENTRY_AUTH_TOKEN
ARG SENTRY_DSN
ARG SENTRY_ENV
ARG SENTRY_ORG
ARG SENTRY_PROJECT
ARG SENTRY_URL

ENV SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN} \
    SENTRY_DSN=${SENTRY_DSN} \
    SENTRY_ENV=${SENTRY_ENV} \
    SENTRY_ORG=${SENTRY_ORG} \
    SENTRY_PROJECT=${SENTRY_PROJECT} \
    SENTRY_URL=${SENTRY_URL}

RUN NODE_OPTIONS="--max-old-space-size=9000" npm run build

CMD ["node", "--import", "./.output/server/sentry.server.config.mjs", ".output/server/index.mjs"]
