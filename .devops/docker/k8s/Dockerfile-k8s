# syntax = docker/dockerfile:experimental
FROM node:16.20.0-bullseye AS frontend

ENV DOCKER_BUILDKIT=1
ENV CHOKIDAR_USEPOLLING=true

ARG APP_ENV

RUN ln -s /usr/bin/python3 /usr/bin/python

COPY ./frontend_src/package.json /frontend_src/
COPY ./frontend_src/package-lock.json /frontend_src/

RUN apt-get update -qq \
  && apt-get install -y \
    build-essential \
    libxi-dev \
    libglu1-mesa-dev \
    libglew-dev \
    pkg-config

SHELL ["/bin/bash", "--login", "-c"]

WORKDIR /frontend_src

RUN npm root
RUN npm install --legacy-peer-deps
RUN npm install --global gulp-cli
RUN npm rebuild node-sass

COPY ./frontend_src /frontend_src
COPY ./src/frontend_cms /src/frontend_cms

RUN gulp build
RUN npm run prod:part1
RUN npm run prod:part2
RUN npm run prod:part3

FROM python:3.11-bookworm as development_build

ENV PYTHONUNBUFFERED=1 \
  POETRY_VERSION=2.1.2

RUN apt-get update -qq \
  && apt-get install -qq -y \
    gcc \
    gettext \
    vim \
    xvfb \
    librsvg2-bin \
    # latex and pdflatex
    texlive-latex-recommended \
    texlive-latex-extra \
    texlive-lang-polish \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean -qq -y \
  && pip install --upgrade pip \
  && pip install "poetry==${POETRY_VERSION}"

RUN curl https://dl.min.io/client/mc/release/linux-amd64/mc --create-dirs -o /usr/local/bin/mc \
  && chmod +x /usr/local/bin/mc

ENV VIRTUAL_ENV=/opt/venv
RUN python3 -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

COPY ./src/poetry.lock ./src/pyproject.toml ./

RUN --mount=type=cache,mode=0777,target=/root/.cache/pip poetry install --no-ansi

ENV HOME=/home/<USER>

COPY ./.devops/docker/backend/entrypoint.sh /entrypoint.sh
RUN sed -i 's/\r//' /entrypoint.sh && chmod +x /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]

COPY ./.devops/docker/k8s/start-k8s.sh /start-k8s.sh
RUN sed -i 's/\r//' /start-k8s.sh && chmod +x /start-k8s.sh
CMD ["/start-k8s.sh"]

COPY ./.devops/docker/k8s/sync-media.sh /app/sync-media.sh
RUN sed -i 's/\r//' /app/sync-media.sh && chmod +x /app/sync-media.sh

COPY ./.devops/docker/k8s/collectstatic.sh /app/collectstatic.sh
RUN sed -i 's/\r//' /app/collectstatic.sh && chmod +x /app/collectstatic.sh

COPY ./.devops/docker/backend/celery/worker/start.sh /start-celeryworker.sh
RUN sed -i 's/\r//' /start-celeryworker.sh \
    && chmod +x /start-celeryworker.sh

COPY ./.devops/docker/backend/celery/beat/start.sh /start-celerybeat.sh
RUN sed -i 's/\r//' /start-celerybeat.sh \
    && chmod +x /start-celerybeat.sh

COPY ./.devops/docker/backend/celery/flower/start.sh /start-flower.sh
RUN sed -i 's/\r//' /start-flower.sh \
    && chmod +x /start-flower.sh

WORKDIR /app

ARG DJANGO_ENV
ARG GIT_COMMIT
ARG GIT_BRANCH
ARG USER_ID=1000

ENV USER_ID=${USER_ID} \
  DJANGO_ENV=${DJANGO_ENV} \
  GIT_COMMIT=${GIT_COMMIT} \
  GIT_BRANCH=${GIT_BRANCH}

# create the user and group to run the Django application as
RUN addgroup --system --gid ${USER_ID} django \
  && adduser --system --home ${HOME} --uid ${USER_ID} --ingroup django django

RUN chown -R django /app /home/<USER>
COPY --from=frontend --chown=django:django /src/frontend_cms /app/frontend_cms
COPY --from=frontend --chown=django:django /frontend_src/webpack-node.config.js  /app/webpack-node.config.js
COPY --from=frontend --chown=django:django /src/frontend_cms/static/dist_vue/webpack-stats.json /app/frontend_cms/static/dist_vue/webpack-stats.json
COPY --chown=django:django ./src /app
COPY --chown=django:django ./shared/ /app/shared/

USER django
