FROM node:16.20.0-bookworm

RUN apt-get update -qq \
  && apt-get install -qq -y \
    vim \
    libgtk2.0-0  \
    libgtk-3-0  \
    libgbm-dev  \
    libnotify-dev  \
    libgconf-2-4  \
    libnss3  \
    libxss1  \
    libasound2  \
    libxtst6  \
    xauth \
    xvfb \
    build-essential \
    libxi-dev \
    libglu1-mesa-dev \
    libglew-dev \
    pkg-config

RUN curl -LO  https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
RUN apt-get install -y ./google-chrome-stable_current_amd64.deb
RUN rm google-chrome-stable_current_amd64.deb

COPY ./cypress/package.json /cypress/
COPY ./cypress/package-lock.json /cypress/

SHELL ["/bin/bash", "--login", "-c"]

WORKDIR /cypress

RUN npm root
RUN npm install

COPY ./cypress /cypress
