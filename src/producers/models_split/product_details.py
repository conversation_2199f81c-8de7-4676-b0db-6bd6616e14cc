from django.contrib.contenttypes.fields import GenericRelation
from django.db import models

from cstm_be.media_storage import private_media_storage
from custom.enums import Sofa01Color
from custom.utils.import_object import import_object
from producers.models_production_files import (
    ProductDetailsJettyProductionFiles,
    ProductDetailsSottyProductionFiles,
    ProductDetailsWattyProductionFiles,
)
from producers.models_split.details_files_versioning import DetailsFilesVersioning
from producers.models_split.file_history_entry import FileHistoryEntry
from producers.utils import RandomizedUploadTo


class ProductDetailsJetty(
    DetailsFilesVersioning,
    ProductDetailsJettyProductionFiles,
    models.Model,
):
    """
    Details model contains additional data.

    Model is 1:1 relation to product and contains serialization and files required
    to prepare batch files.
    """

    file_history_entries = GenericRelation(
        FileHistoryEntry,
        object_id_field='parent_id',
        limit_choices_to=(models.Q(app_label='producers', model='ProductDetailsJetty')),
    )

    product = models.OneToOneField(
        'producers.Product',
        related_name='product_details_jetty',
        on_delete=models.CASCADE,
    )

    instruction = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/instruction/%Y/%m'),
    )
    cnc_connections = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/cnc_connections/%Y/%m'),
    )
    cnc_connections_zip = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/cnc_connections_zip/%Y/%m'),
    )

    front_view = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/front_view/%Y/%m'),
    )
    # Some manufacturers need different front views files
    front_view_zip = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/front_view_zip/%Y/%m'),
    )
    horizontals_pdf = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/horizontals_pdf/%Y/%m'),
    )
    vertical_labels = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/vertical_labels/%Y/%m'),
    )

    nesting_data_for_batch = models.TextField(blank=True, default='')  # NOT USED

    packaging_instruction = models.FileField(
        blank=True,
        null=True,
        upload_to=RandomizedUploadTo('producers/product/packaging_instruction/%Y/%m'),
        storage=private_media_storage,
    )  # packaging PDF
    production_drawings = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/production_drawings/%Y/%m'),
    )
    package_labels = models.TextField(blank=True, default='')  # NOT USED

    cached_serialization = models.JSONField(null=True, blank=True)

    def __str__(self):
        return f'ProductDetailsJetty[product={self.product}]'

    @property
    def serializer(self):
        return import_object('producers.serializers.ProductDetailsJettySerializer')

    def get_serialized(self):
        return self.serializer(self).data

    def get_material_name(self, get_for_batch=False):
        if (
            self.product.order_item is None
            or self.product.order_item.order_item is None
        ):
            return ''
        item = self.product.order_item.order_item
        return item.get_material_name(get_for_batch=get_for_batch)

    def save(self, *args, **kwargs):
        """Use save event to check if all required files are set and batch
        should recalculate."""
        files_changed = self.check_for_file_changes(**kwargs)
        super().save(*args, **kwargs)
        # First case is standard update of product (either task queue or inline).
        # We check only for files changes
        # Second case is when we are creating details (by accessing property)
        # and batching at the same time in tests
        if (not self._state.adding and files_changed and self.product.batch) or (
            self._state.adding and self.product.batch
        ):
            self.product.batch.actions_needed_flow.process_product_files_recalculated()

    @property
    def prefetched_file_history(self):
        if hasattr(self, 'jetty_file_history_entries'):
            return self.jetty_file_history_entries


class ProductDetailsWatty(
    DetailsFilesVersioning,
    ProductDetailsWattyProductionFiles,
    models.Model,
):
    file_history_entries = GenericRelation(
        FileHistoryEntry,
        object_id_field='parent_id',
        limit_choices_to=(models.Q(app_label='producers', model='ProductDetailsWatty')),
    )
    product = models.OneToOneField(
        'producers.Product',
        related_name='product_details_watty',
        on_delete=models.CASCADE,
    )
    cached_serialization = models.JSONField(default=dict, blank=True)

    instruction = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/instruction/%Y/%m'),
    )
    cnc_connections_zip = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/cnc_connections_zip/%Y/%m'),
    )
    front_view = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/front_view/%Y/%m'),
    )
    # Some manufacturers need different front views files
    front_view_zip = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/front_view_zip/%Y/%m'),
    )
    production_drawings = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/production_drawings/%Y/%m'),
    )
    packaging_instruction = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/packaging_instruction/%Y/%m'),
    )

    @property
    def serializer(self):
        return import_object('producers.serializers.ProductDetailsWattySerializer')

    def get_serialized(self):
        return self.serializer(self).data

    def get_material_name(self, get_for_batch=False):
        # copy-paste from PDJ
        if (
            self.product.order_item is None
            or self.product.order_item.order_item is None
        ):
            return ''
        item = self.product.order_item.order_item
        return item.get_material_name(get_for_batch=get_for_batch)

    def save(self, *args, **kwargs):
        """Use save event to check if all required files are set and batch
        should recalculate."""
        # TODO: this is a copy-paste from PDJ, move logic to DetailsFilesVersioning
        files_changed = self.check_for_file_changes(**kwargs)

        # First case is standard update of product (either task queue or inline).
        # We check only for files changes
        # Second case is when we are creating details (by accessing property)
        # and batching at the same time in tests
        if (not self._state.adding and files_changed and self.product.batch) or (
            self._state.adding and self.product.batch
        ):
            self.product.batch.actions_needed_flow.process_product_files_recalculated()
        super().save(*args, **kwargs)

    @property
    def prefetched_file_history(self):
        if hasattr(self, 'watty_file_history_entries'):
            return self.watty_file_history_entries

    def __str__(self):
        return f'ProductDetailsWatty[product={self.product}]'


class ProductDetailsSotty(
    DetailsFilesVersioning,
    ProductDetailsSottyProductionFiles,
    models.Model,
):
    file_history_entries = GenericRelation(
        FileHistoryEntry,
        object_id_field='parent_id',
        limit_choices_to=(models.Q(app_label='producers', model='ProductDetailsSotty')),
    )
    product = models.OneToOneField(
        'producers.Product',
        related_name='product_details_sotty',
        on_delete=models.CASCADE,
    )
    cached_serialization = models.JSONField(default=dict, blank=True)

    front_view = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/front_view/%Y/%m'),
    )
    labels_packaging = models.FileField(
        verbose_name='Etykiety pakowanie',
        blank=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/products/labels_packaging/%Y/%m'),
    )
    instruction = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/product/instruction/%Y/%m'),
    )

    @property
    def serializer(self):
        return import_object('producers.serializers.ProductDetailsSottySerializer')

    def get_serialized(self):
        return self.serializer(self).data

    def save(self, *args, **kwargs):
        """Use save event to check if all required files are set and batch
        should recalculate."""
        # TODO: this is a copy-paste from PDJ, move logic to DetailsFilesVersioning
        files_changed = self.check_for_file_changes(**kwargs)

        # First case is standard update of product (either task queue or inline).
        # We check only for files changes
        # Second case is when we are creating details (by accessing property)
        # and batching at the same time in tests
        if (not self._state.adding and files_changed and self.product.batch) or (
            self._state.adding and self.product.batch
        ):
            self.product.batch.actions_needed_flow.process_product_files_recalculated()
        super().save(*args, **kwargs)

    @property
    def prefetched_file_history(self):
        if hasattr(self, 'sotty_file_history_entries'):
            return self.sotty_file_history_entries

    def get_material_name(self, get_for_batch=False) -> str:
        if (
            self.product.order_item is None
            or self.product.order_item.order_item is None
        ):
            return ''
        gallery_item = self.product.order_item.order_item
        return ', '.join(
            [
                f'{gallery_item.fabric.translated_name} '
                f'{Sofa01Color(material).translated_color})'
                for material in gallery_item
            ]
        )

    def __str__(self):
        return f'ProductDetailsSotty[product={self.product}]'
