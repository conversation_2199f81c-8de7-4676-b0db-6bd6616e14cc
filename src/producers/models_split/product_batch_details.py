from django.contrib.contenttypes.fields import GenericRelation
from django.db import models

from cstm_be.media_storage import private_media_storage
from custom.utils.import_object import import_object
from producers.models_production_files import (
    ProductBatchJettyDetailsProductionFiles,
    ProductBatchSottyDetailsProductionFiles,
    ProductBatchWattyDetailsProductionFiles,
)
from producers.models_split.details_files_versioning import BatchDetailsFilesVersioning
from producers.models_split.file_history_entry import FileHistoryEntry
from producers.utils import RandomizedUploadTo


class ProductBatchDetailsJetty(
    BatchDetailsFilesVersioning,
    ProductBatchJettyDetailsProductionFiles,
    models.Model,
):
    file_history_entries = GenericRelation(
        FileHistoryEntry,
        object_id_field='parent_id',
        limit_choices_to=(
            models.Q(app_label='producers', model='ProductBatchDetailsJetty')
        ),
    )
    product_batch = models.OneToOneField(
        'producers.ProductBatch',
        related_name='batch_details_jetty',
        on_delete=models.CASCADE,
    )
    nameplate = models.FileField(
        verbose_name='Tabliczka znamionowa',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nameplate/%Y/%m'),
    )
    nesting = models.FileField(
        verbose_name='Rozkroj elementy podstawowe',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting/%Y/%m'),
    )  # base nesting file, 3.1
    nesting_backs = models.FileField(
        verbose_name='Rozkroj sciany tylne',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_backs/%Y/%m'),
    )  # secondary nesting file, 3.2
    nesting_drawers = models.FileField(
        verbose_name='Rozkroj elementy szuflad',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_drawers/%Y/%m'),
    )  # 3.3
    nesting_bottom_drawers = models.FileField(
        verbose_name='Rozkroj dno szuflad',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_bottom_drawers/%Y/%m'),
    )  # 3.4
    nesting_front_drawers = models.FileField(
        verbose_name='Rozkroj drzwi frontow szuflad',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_front_drawers/%Y/%m'),
    )  # 3.9
    nesting_handle_blende = models.FileField(
        verbose_name='Rozkroj blendy uchwytu',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_handle_blende/%Y/%m'),
    )
    nesting_desk_beam = models.FileField(
        verbose_name='Rozkroj poprzeczki biurka',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_desk_beam/%Y/%m'),
    )
    labels_elements = models.FileField(
        verbose_name='Etykiety elementy',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/labels_elements/%Y/%m'),
    )  # 3.5

    labels_packaging = models.FileField(
        verbose_name='Etykiety pakowanie',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/labels_packaging/%Y/%m'),
    )  # 3.6

    labels_logistic = models.FileField(
        verbose_name='Etykiety logistyka',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/labels_logistic/%Y/%m'),
    )  # 3.7

    labels_verticals = models.FileField(
        verbose_name='Etykiety piony',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/labels_verticals/%Y/%m'),
    )

    packaging_csvs = models.FileField(
        verbose_name='Rozkroj pakowanie',
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/packaging_csv/%Y/%m'),
    )  # cartoner nesting files, CSV/txt, 3.8

    nesting_zip = models.FileField(
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_zip/%Y/%m'),
    )

    meblepl_zip = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/meblepl_zip/%Y/%m'),
    )

    horizontal_pdfs_zip = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/horizontal_pdfs_zip/%Y/%m'),
    )
    cardboard_beams_xls = models.FileField(
        verbose_name='Rozkroj kątowników i ceowników',
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/cardboard_beams_xls/%Y/%m'),
    )
    accessories_packaging_list = models.FileField(
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo(
            'producers/batch/accessories_packaging_list/%Y/%m'
        ),
    )

    rotated_elements = models.FileField(
        verbose_name='Lista obróconych elementów',
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/rotated_elements/%Y/%m'),
    )

    @property
    def serializer(self):
        return import_object('producers.serializers.ProductBatchDetailsJettySerializer')

    def get_serialized(self):
        return self.serializer(self).data

    def save(self, *args, **kwargs):
        files_changed = self.check_for_file_changes(**kwargs)
        if files_changed:
            self._update_batch_action_needed(self.product_batch)
        super().save(*args, **kwargs)

    @property
    def prefetched_file_history(self):
        if hasattr(self, 'jetty_file_history_entries'):
            return self.jetty_file_history_entries


class ProductBatchDetailsWatty(
    BatchDetailsFilesVersioning,
    ProductBatchWattyDetailsProductionFiles,
    models.Model,
):
    file_history_entries = GenericRelation(
        FileHistoryEntry,
        object_id_field='parent_id',
        limit_choices_to=(
            models.Q(app_label='producers', model='ProductBatchDetailsWatty')
        ),
    )
    product_batch = models.OneToOneField(
        'producers.ProductBatch',
        related_name='batch_details_watty',
        on_delete=models.CASCADE,
    )

    nameplate = models.FileField(
        verbose_name='Tabliczka znamionowa',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nameplate/%Y/%m'),
    )
    nesting_zip = models.FileField(
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_zip/%Y/%m'),
    )  # 3.1

    nesting_bar = models.FileField(
        verbose_name='Rozkroj drazek',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_bar/%Y/%m'),
    )  # 3.2

    nesting_led_profile = models.FileField(
        verbose_name='Rozkroj profili LED',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_led_profile/%Y/%m'),
    )  # 3.?

    nesting_mask = models.FileField(
        verbose_name='Rozkroj maskownica',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_mask/%Y/%m'),
    )  # 3.3

    nesting_drawer_synchro = models.FileField(
        verbose_name='Rozkroj synchronizator szuflad',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_drawer_synchro/%Y/%m'),
    )  # 3.4

    lighting_completion_list = models.FileField(
        verbose_name='Lista kompletacji oswietlenia',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/lighting_completion_list/%Y/%m'),
    )  # 3.?

    labels_elements = models.FileField(
        verbose_name='Etykiety elementy',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/labels_elements/%Y/%m'),
    )  # 3.5

    labels_packaging = models.FileField(
        verbose_name='Etykiety pakowanie',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/labels_packaging/%Y/%m'),
    )  # 3.6

    labels_adapters = models.FileField(
        verbose_name='Etykiety adaptery',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/labels_adapters/%Y/%m'),
    )

    labels_logistic = models.FileField(
        verbose_name='Etykiety logistyka',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/labels_logistic/%Y/%m'),
    )  # 3.7

    packaging_csvs = models.FileField(
        verbose_name='Rozkroj pakowanie',
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/packaging_csvs/%Y/%m'),
    )  # 3.8

    rotated_elements = models.FileField(
        verbose_name='Lista obróconych elementów',
        blank=True,
        null=True,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/rotated_elements/%Y/%m'),
    )

    nesting_hang_slat = models.FileField(
        verbose_name='Rozkroj listwa montażowa',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_hang_slat/%Y/%m'),
    )

    @property
    def serializer(self):
        return import_object('producers.serializers.ProductBatchDetailsWattySerializer')

    def get_serialized(self):
        return self.serializer(self).data

    @property
    def prefetched_file_history(self):
        if hasattr(self, 'watty_file_history_entries'):
            return self.watty_file_history_entries

    def save(self, *args, **kwargs):
        files_changed = self.check_for_file_changes(**kwargs)
        if files_changed:
            self._update_batch_action_needed(self.product_batch)
        super().save(*args, **kwargs)


class ProductBatchDetailsSotty(
    BatchDetailsFilesVersioning,
    ProductBatchSottyDetailsProductionFiles,
    models.Model,
):
    file_history_entries = GenericRelation(
        FileHistoryEntry,
        object_id_field='parent_id',
        limit_choices_to=(
            models.Q(app_label='producers', model='ProductBatchDetailsSotty')
        ),
    )
    product_batch = models.OneToOneField(
        'producers.ProductBatch',
        related_name='batch_details_sotty',
        on_delete=models.CASCADE,
    )
    labels_elements = models.FileField(
        verbose_name='Etykiety elementy',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/labels_elements/%Y/%m'),
    )
    labels_packaging = models.FileField(
        verbose_name='Etykiety pakowanie',
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/labels_packaging/%Y/%m'),
    )

    @property
    def serializer(self):
        return import_object('producers.serializers.ProductBatchDetailsSottySerializer')

    def get_serialized(self):
        return self.serializer(self).data

    @property
    def prefetched_file_history(self):
        if hasattr(self, 'sotty_file_history_entries'):
            return self.sotty_file_history_entries

    def save(self, *args, **kwargs):
        files_changed = self.check_for_file_changes(**kwargs)
        if files_changed:
            self._update_batch_action_needed(self.product_batch)
        super().save(*args, **kwargs)
