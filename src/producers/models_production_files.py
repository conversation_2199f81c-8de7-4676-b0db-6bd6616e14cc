# flake8: noqa
import io
import json
import logging
import mimetypes
import random
import shutil
import string

from typing import Optional
from uuid import UUID
from zipfile import ZipFile

from django.conf import (
    os,
    settings,
)
from django.core.files import File
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db import transaction
from django.utils import translation
from django.utils.encoding import force_bytes

from celery import chain

from custom.enums import (
    Furniture,
    LanguageEnum,
    PhysicalProductVersion,
    ShelfType,
)
from custom.google_headless import ChromeHeadless
from producers.admin_generate_instruction import generate_instruction_in_latex
from producers.enums import Manufacturers
from producers.gh.moduly_instrukcja import Ivy_instrukcja
from producers.internal_api.events import ProductRefreshEvent
from producers.production_system_utils.client import ProductionSystemClient
from producers.production_system_utils.enums import FileType
from producers.production_system_utils.retry_connection import retry_request
from producers.utils import unzip_file_to_another_zip

logger = logging.getLogger('producers')

EXCEL_COLUMN_VERY_SMALL = 256 * 10
EXCEL_COLUMN_SMALL = 256 * 15
EXCEL_COLUMN_MEDIUM = 256 * 25
EXCEL_COLUMN_LARGE = 256 * 35
EXCEL_COLUMN_VERY_LARGE = 256 * 40


class PSProductionFilesMixin:
    @retry_request()
    def request_batch_file_from_ps(self, file_type: FileType) -> None:
        request_id = self.process_file_request_sent(file_type.field_name)
        transaction.on_commit(
            lambda: self._send_request_batch_file_from_ps(
                file_type,
                request_id,
            ),
        )

    def _send_request_batch_file_from_ps(
        self,
        file_type: FileType,
        request_id: UUID,
    ) -> None:
        with ProductionSystemClient(suppress_errors=False) as ps_client:
            ps_client.request_batch_file_from_ps(
                batch=self.product_batch,
                file_type=file_type,
                request_id=request_id,
            )

    @retry_request()
    def request_product_file_from_ps(
        self,
        file_type: FileType,
        anonymous=False,
    ) -> None:
        request_id = self.process_file_request_sent(file_type.field_name)
        transaction.on_commit(
            lambda: self._send_request_product_file_from_ps(
                file_type,
                request_id,
                anonymous,
            ),
        )

    def _send_request_product_file_from_ps(
        self,
        file_type: FileType,
        request_id: UUID,
        anonymous: bool,
    ) -> None:
        with ProductionSystemClient(suppress_errors=False) as ps_client:
            ps_client.request_product_file_form_ps(
                product=self.product,
                file_type=file_type,
                request_id=request_id,
                anonymous=anonymous,
            )


class ProductDetailsJettyProductionFiles(PSProductionFilesMixin):
    # 2.1
    def generate_front_view(self):
        self.request_product_file_from_ps(file_type=FileType.FRONT_VIEW_PDF)
        if self.product.manufactor_id in {
            Manufacturers.DREWTUR,
            Manufacturers.STUDIO_93,
        }:
            self.request_product_file_from_ps(file_type=FileType.FRONT_VIEW_ZIP)
        ProductRefreshEvent(self.product)

    # 2.2
    def generate_packaging_instruction(self, force_refresh=False):
        from producers.views import ProductionPrintPackagingPdf

        production_ivy = self.product.get_production_ivy()
        kwargs = {'pk': self.product.id}
        v = ProductionPrintPackagingPdf(**kwargs)
        v.kwargs = kwargs
        context = v.get_context_data()
        country_pl = self.product.get_country_name_for_packaging_instruction()
        drawings = production_ivy.final_packaging_drawings_BE(
            svg=True,
            one_pack_per_pdf=(
                self.product.manufactor_id
                in {Manufacturers.DREWTUR, Manufacturers.STUDIO_93}
            ),
            country_pl=country_pl,
        )
        merged_drawings = None
        if self.product.manufactor_id == Manufacturers.STUDIO_93:
            # for now (02.2024) S93 wants both packaging instructions per pack
            #  and as one PDF.
            merged_drawings = production_ivy.final_packaging_drawings_BE(
                svg=True,
                one_pack_per_pdf=False,
                country_pl=country_pl,
            )
        context['svgs'] = [x[0] for x in drawings]
        file_name = '{}_pakowanie.pdf'.format(self.product.get_product_id())

        chrome = ChromeHeadless()
        if self.product.manufactor_id in {
            Manufacturers.DREWTUR,
            Manufacturers.STUDIO_93,
        }:
            # Drewtur wants them named same as their barcodes contents
            mf = io.BytesIO()
            with ZipFile(mf, mode='w') as zf:
                for package_number, svg in enumerate(context['svgs'], start=1):
                    single_context = {
                        'svgs': [svg],
                        'view': context['view'],
                    }
                    pdf_bytes = self._get_pdf_from_context(chrome, v, single_context)
                    name = self._get_custom_name(package_number, len(context['svgs']))
                    zf.writestr(name, pdf_bytes)
                if merged_drawings:
                    # S93 needs to have files for Type01v named like Type01
                    name = file_name.replace('F1', 'T1')
                    pdf_bytes = self._get_pdf_from_context(chrome, v, context)
                    zf.writestr(name, pdf_bytes)
            file_name = f'{self.product.id}_pakowanie.zip'
            suf = SimpleUploadedFile(
                file_name, mf.getvalue(), content_type='application/zip'
            )
        else:
            pdf_bytes = self._get_pdf_from_context(chrome, v, context)
            suf = SimpleUploadedFile(
                file_name, pdf_bytes, content_type='application/pdf'
            )
        self.packaging_instruction.save(file_name, suf, save=False)
        self.save(update_fields=['packaging_instruction'])
        ProductRefreshEvent(self.product)

    def _get_custom_name(self, package_number, no_of_packs) -> str:
        if self.product.manufactor_id == Manufacturers.STUDIO_93:
            # S93 needs to have files for Type01v named like Type01
            return (
                f'{self.product.cached_shelf_type}'
                + f'-{self.product.id}'
                + f'-{package_number}'
                + f'-{no_of_packs}.pdf'
            ).replace('F1', 'T1')
        return f'{self.product.id}-{package_number}.pdf'

    @staticmethod
    def _get_pdf_from_context(chrome, v, context):
        return chrome.get_pdf_from_template(
            v.get_template_names()[0],
            context,
            paper_width=210,
            paper_height=297,
            margins=False,
        )

    # 2.3
    def generate_connections_zip(self):
        self.request_product_file_from_ps(file_type=FileType.GCODES)

    def generate_instruction(self, anonymous=False):
        """Generate instruction with empty user_name if anonymous."""
        user_name = ''
        if not anonymous:
            user_name = '{} {}'.format(
                self.product.order.first_name, self.product.order.last_name
            )

        self.generate_instruction_with_username(user_name)
        ProductRefreshEvent(self.product)
        self.product.order.emit_product_passport_ready_event()

    def generate_instruction_with_username(self, user_name: Optional['str']):
        if self.product.reproduction_origin_complaint is not None:
            return  # no manual for reproductions

        order = self.product.order
        ivy_production = self.product.get_production_ivy()
        # get language from user to override text in manual
        owner_language = order.owner.profile.language
        # we overwrite spanish language in manual to eng for know
        language_code = LanguageEnum(owner_language).get_jetty_instruction_language()
        with translation.override(language_code):
            instruction = Ivy_instrukcja.Instrukcja(
                ivy_production,
                user_name=user_name,
                language=language_code,
            )
            pages = instruction.gen_pages()
            pages_svg = pages['svg']
            pages_json = pages['json']
        r = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
        tmp_dir = f'/tmp/instruction_{r}_{self.product.id}'

        if not os.path.exists(tmp_dir):
            os.makedirs(tmp_dir)

        for index, page_json in enumerate(pages_json):
            f = open('{}/tmp_file{}.txt'.format(tmp_dir, index + 1), 'w+')
            f.writelines(json.dumps([page_json], indent=4))
            f.close()
        for svg in pages_svg:
            with open('{}/{}'.format(tmp_dir, svg[1]), 'w+') as f:
                f.write(svg[0])

        generate_instruction_in_latex(tmp_dir, settings.BASE_DIR, language_code)
        with open(f'{tmp_dir}/instruction.pdf', 'rb') as f:
            file_name = '{}_instruction.pdf'.format(self.product.get_product_id())
            if self.product.manufactor_id == Manufacturers.STUDIO_93:
                # S93 needs to have files for Type01v named like Type01
                file_name = file_name.replace('F1', 'T1')
            self.instruction.save(file_name, File(f), save=False)
        self.save(update_fields=['instruction'])
        shutil.rmtree(tmp_dir)

    def write_cnc_connections_to_zip(self, zf):
        if not self.cnc_connections_zip:
            raise ValueError('Gcodes files not present in ProductDetails.')

        with ZipFile(self.cnc_connections_zip) as gcodes_zf:
            for filename in gcodes_zf.namelist():
                zf.writestr(
                    filename,
                    gcodes_zf.open(filename).read(),
                )

    def generate_connections_dxf(self):
        images = self.product.get_production_ivy().final_connections_drawings_BE(
            dxf=True
        )
        dxf_string = io.StringIO()
        images[0][0].write(dxf_string)
        file_name = '{}_CNC.dxf'.format(self.product.get_product_id())
        suf = SimpleUploadedFile(
            file_name,
            force_bytes(dxf_string.getvalue()),
            content_type='application/dxf',
        )
        self.cnc_connections.save(file_name, suf, save=False)
        self.save(update_fields=['cnc_connections'])

    def generate_production_drawings(self):
        """Telmex uses production drawings to produce blende in T01v shelves."""
        self.request_product_file_from_ps(
            file_type=FileType.PRODUCTION_DRAWINGS,
        )


class ProductBatchJettyProductionFiles:
    @property
    def shelf_type(self) -> Optional[int]:
        """Sometimes shelf type (for jetty and watty), sometimes None."""
        # TODO: this is very wrongish
        if self.product_type in {'jetty', 'watty', 'sotty'}:
            return self.batch_items.first().order_item.order_item.shelf_type
        else:
            return None

    @property
    def unified_shelf_code(self) -> str:
        """Shelf code that doesn't distinguish between T1 and F1."""
        # TODO: investigate and cleanse our soul of this abomination
        if self.shelf_type == ShelfType.VENEER_TYPE01:
            return 'T1'
        return ShelfType(self.shelf_type).production_code

    def generate_all_files(self, request=None):
        from producers.tasks import (
            generate_all_batch_files_task,
            generate_meblepl_package_task,
        )

        self.actions_needed_flow.process_batch_files_recalculated_queued()
        if self.manufactor.name == self.manufactor.MEBLE_PL:
            chain(
                generate_all_batch_files_task.s(self.id),
                generate_meblepl_package_task.s(batch_id=self.id),
            ).apply_async()
        else:
            generate_all_batch_files_task.delay(self.id)
        return

    def generate_nesting_files(self):
        self.details.generate_nesting_zip()

    def generate_instructions(self):
        for product in self.batch_items.all():
            product.details.generate_instruction()

    # labels_elements, 3.5
    def generate_labels_for_elements(self):
        self.details.generate_labels_elements()

    # labels_packaging, 3.6
    def generate_labels_for_packaging(self):
        self.details.generate_labels_packaging()

    # cardboard beams list xls
    def generate_cardboard_beams_xls(self):
        self.details.generate_cardboard_beams_xls()

    # 3.11 meblepl zip structure
    def generate_meblepl_package(self):
        stream = io.BytesIO()
        with ZipFile(stream, 'w') as zipfile:
            for product in self.batch_items.all():
                details = product.details
                # elements w roocie
                zipfile.writestr(
                    'ZAMOWIENIA/{filename}'.format(
                        filename=product.get_elements_csv_meble_pl_filename(),
                    ),
                    product.get_production_ivy().get_elements_csv_meble_pl(batch=self),
                )

                if self.should_add_plywood_file(product):
                    # elements.csv with new plywood
                    zipfile.writestr(
                        'ZAMOWIENIA SKLEJKA POPRZECZNA/{filename}'.format(
                            filename=product.get_elements_csv_meble_pl_filename(),
                        ),
                        product.get_production_ivy().get_elements_csv_meble_pl(
                            batch=self,
                            is_new_plywood=True,
                        ),
                    )

                # schemat pakowania
                zipfile.writestr(
                    'SCHEMATY_PAKOWANIA/{}_elements.pdf'.format(
                        product.id,
                    ),
                    details.packaging_instruction.read(),
                )

                # mpr
                with ZipFile(details.cnc_connections_zip, 'a') as zf:
                    for name in zf.namelist():
                        # take only .mpr files, as in zip we also have dxf
                        # all .mpr files are moved into 'MPR' folder
                        if 'mpr/' in name or '.mpr' in name:
                            new_filename = 'MPR/{filename}'.format(
                                filename=name.split('/')[-1],
                            )
                            zipfile.writestr(
                                new_filename,
                                zf.open(name).read(),
                            )

                # kartony
                with ProductionSystemClient(suppress_errors=False) as ps_client:
                    cartoner_zip_data = ps_client.get_product_test_files(
                        product,
                        'cartoner',
                    )

                unzip_file_to_another_zip(
                    source_zip=io.BytesIO(cartoner_zip_data),
                    target_zip=zipfile,
                    path_in_target_zip='KARTONY/',
                )

                # instrukcje
                if not product.is_complaint():
                    if not (
                        details.instruction
                        and details.instruction.storage.exists(details.instruction.name)
                    ):
                        details.generate_instruction()
                    zipfile.writestr(
                        'INSTRUKCJE/{}_elements.pdf'.format(product.id),
                        details.instruction.read(),
                    )
                # front
                zipfile.writestr(
                    'FRONT/{}_elements.pdf'.format(product.id),
                    details.front_view.read(),
                )
            zipfile.writestr(
                'TABLICZKA_ZNAMIONOWA/{}'.format(
                    os.path.basename(self.details.nameplate.name)
                ),
                self.details.nameplate.read(),
            )
            # etykiety, czytane z zipa
            with ZipFile(self.details.labels_elements, 'a') as zf:
                for name in zf.namelist():
                    zipfile.writestr(
                        'ETYKIETY/{}'.format(name),
                        zf.open(name).read(),
                    )

            # etykiety pakowanie, czytane z zipa
            with ZipFile(self.details.labels_packaging, 'a') as zf:
                for name in zf.namelist():
                    zipfile.writestr(
                        'ETYKIETY_PAKOWANIE/{}'.format(name),
                        zf.open(name).read(),
                    )

        file_name = 'B{}_meblepl.zip'.format(self.id)
        suf = SimpleUploadedFile(
            file_name,
            stream.getvalue(),
            content_type='application/zip',
        )
        self.details.meblepl_zip.save(file_name, suf, save=False)
        self.details.save(update_fields=['meblepl_zip'])

    @property
    def has_vertical_labels(self):
        """
        Vertical labels are generated for Drewtur, Novum, Studio 93, Inex.

        All batches for Drewtur have vertical labels. The rest of
        manufacturers have vertical labels only for DIPLO+.
        """
        if self.manufactor.id not in {
            Manufacturers.DREWTUR,
            Manufacturers.NOVUM,
            Manufacturers.STUDIO_93,
            Manufacturers.INEX,
        }:
            return False
        any_diplo_or_newer = any(
            ppv >= PhysicalProductVersion.DIPLO
            for ppv in self.batch_items.values_list(
                'cached_physical_product_version',
                flat=True,
            )
        )
        return any_diplo_or_newer or self.manufactor.id == Manufacturers.DREWTUR

    # 3.15 vertical labels
    def generate_vertical_labels(self):
        if not self.has_vertical_labels:
            return
        self.details.request_batch_file_from_ps(file_type=FileType.LABELS_VERTICALS)

    def _guess_content_type_from_ps_data(self, file_name, default='application/zip'):
        content_type = mimetypes.guess_type(file_name)
        if content_type[0] is None:
            return default
        return content_type[0]

    def _get_extension_from_ps_filename(self, ps_file_name, default='zip'):
        if '.' in ps_file_name:
            return ps_file_name.rsplit('.', 1)[1]
        return default

    def should_add_plywood_file(self, product) -> bool:
        """For stego and newer type 2 we don't want plywood drawer internals file."""
        return not (
            product.cached_physical_product_version >= PhysicalProductVersion.STEGO
            and product.cached_shelf_type == 'T2'
        )


class ProductBatchDetailsProductionFilesMixin(PSProductionFilesMixin):
    def generate_packaging_csvs(self):
        self.request_batch_file_from_ps(file_type=FileType.CARTONER)

    def generate_labels_packaging(self):
        self.request_batch_file_from_ps(file_type=FileType.LABELS_PACKAGING)

    def generate_labels_elements(self):
        self.request_batch_file_from_ps(file_type=FileType.LABELS_ELEMENTS)

    def generate_cardboard_beams_xls(self):
        self.request_batch_file_from_ps(file_type=FileType.BEAMS_LIST)

    def generate_nesting_zip(self):
        self.request_batch_file_from_ps(file_type=FileType.NESTINGS)

    def generate_rotated_elements(self):
        if self.product_batch.manufactor.id == Manufacturers.CENTER_MEBEL:
            self.request_batch_file_from_ps(file_type=FileType.ROTATED_ELEMENTS)

    def generate_nameplate_txt(self):
        if self.nameplate:
            self.nameplate.delete(save=False)
        product_ids = self.product_batch.batch_items.all().values_list('id', flat=True)
        product_ids = [
            f'{product_id:_}'.replace('_', ' ') for product_id in product_ids
        ]
        content = ' \n'.join(product_ids)
        file_name = f'{self.product_batch.get_batch_type()}_B{self.product_batch.id}_tabliczka.txt'
        if self.product_batch.manufactor_id == Manufacturers.STUDIO_93:
            # S93 needs to have files for Type01v named like Type01
            file_name = file_name.replace('F1', 'T1')
        self.nameplate.save(
            file_name,
            ContentFile(content.encode('utf-8')),
            save=False,
        )
        self.save(update_fields=['nameplate'])


class ProductBatchJettyDetailsProductionFiles(ProductBatchDetailsProductionFilesMixin):
    pass


class ProductBatchWattyDetailsProductionFiles(ProductBatchDetailsProductionFilesMixin):
    """Mixin for ProductBatchDetailsWatty responsible for fetching production files."""

    def generate_nesting_bar(self):
        self.request_batch_file_from_ps(file_type=FileType.BAR_NESTINGS)

    def generate_nesting_mask(self):
        self.request_batch_file_from_ps(file_type=FileType.MASK_NESTINGS)

    def generate_nesting_hang_slat(self):
        if self.product_batch.shelf_type == ShelfType.TYPE24:
            self.request_batch_file_from_ps(file_type=FileType.HANG_SLAT_NESTINGS)

    def generate_nesting_synchro(self):
        self.request_batch_file_from_ps(file_type=FileType.SYNCHRO_NESTINGS)

    def generate_nesting_led_profiles(self):
        self.request_batch_file_from_ps(file_type=FileType.LED_PROFILE_NESTINGS)

    def generate_lighting_completion_list(self):
        self.request_batch_file_from_ps(file_type=FileType.LIGHTING_COMPLETION)

    def generate_rotated_elements(self):
        if self.product_batch.manufactor.id == Manufacturers.CENTER_MEBEL:
            self.request_batch_file_from_ps(file_type=FileType.ROTATED_ELEMENTS)

    def generate_labels_adapters(self):
        if (
            self.product_batch.manufactor.id == Manufacturers.DREWTUR
            and self.product_batch.shelf_type
            in {ShelfType.TYPE13, ShelfType.VENEER_TYPE13}
        ):
            self.request_batch_file_from_ps(file_type=FileType.LABELS_ADAPTERS)

    def generate_all_files(self):
        self.generate_nesting_zip()
        self.generate_nesting_bar()
        self.generate_nesting_mask()
        self.generate_nesting_synchro()
        self.generate_nesting_hang_slat()
        self.generate_nesting_led_profiles()
        self.generate_lighting_completion_list()
        self.generate_labels_elements()
        self.generate_labels_packaging()
        self.generate_labels_adapters()
        self.generate_packaging_csvs()
        self.generate_rotated_elements()
        self.generate_nameplate_txt()


class ProductDetailsWattyProductionFiles(PSProductionFilesMixin):
    """Mixin for ProductDetailsWatty responsible for generating production files."""

    def generate_packaging_instruction(self):
        self.request_product_file_from_ps(
            file_type=FileType.PACKAGING,
        )
        ProductRefreshEvent(self.product)

    def generate_front_view(self):
        self.request_product_file_from_ps(file_type=FileType.FRONT_VIEW_PDF)
        if self.product.manufactor_id in {
            Manufacturers.DREWTUR,
            Manufacturers.STUDIO_93,
        }:
            self.request_product_file_from_ps(file_type=FileType.FRONT_VIEW_ZIP)
        ProductRefreshEvent(self.product)

    def generate_connections_zip(self):
        self.request_product_file_from_ps(
            file_type=FileType.GCODES,
        )

    def generate_production_drawings(self):
        self.request_product_file_from_ps(
            file_type=FileType.PRODUCTION_DRAWINGS,
        )

    def write_cnc_connections_to_zip(self, zipfile):
        if not self.cnc_connections_zip:
            # NOTE: not sure if we should attempt to request files here
            self.generate_connections_zip()
            raise ValueError(
                'Gcodes files not present in ProductDetails. '
                + 'Attempting to generate.'
            )
        filepath = (
            'PLIKI_LINIA/'
            if self.product.manufactor_id == 33  # CenterMebel
            else f'CNC/{self.product.id}/'
        )
        with ZipFile(self.cnc_connections_zip) as gcodes_zf:
            for filename in gcodes_zf.namelist():
                zipfile.writestr(
                    f'{filepath}{filename}',
                    gcodes_zf.open(filename).read(),
                )

    def generate_instruction(self, anonymous=False):
        if (
            self.product.shelf_code == 'W3'
            or self.product.reproduction_origin_complaint is not None
        ):
            return  # Manual is not implemented for T03 wardrobes and complaints
        self.request_product_file_from_ps(FileType.MANUAL, anonymous)


class ProductBatchSottyDetailsProductionFiles(ProductBatchDetailsProductionFilesMixin):
    def generate_labels_elements(self):
        self.request_batch_file_from_ps(file_type=FileType.LABELS_ELEMENTS)

    def generate_labels_packaging(self):
        self.request_batch_file_from_ps(file_type=FileType.LABELS_PACKAGING)


class ProductDetailsSottyProductionFiles(PSProductionFilesMixin):
    """Mixin for ProductDetailsSotty responsible for generating production files."""

    def generate_front_view(self):
        self.request_product_file_from_ps(file_type=FileType.FRONT_VIEW_PDF)
        ProductRefreshEvent(self.product)

    def generate_labels_packaging(self):
        self.request_product_file_from_ps(file_type=FileType.LABELS_PACKAGING)

    def generate_instruction(self, anonymous=False):
        self.request_product_file_from_ps(
            file_type=FileType.MANUAL, anonymous=anonymous
        )
        ProductRefreshEvent(self.product)
