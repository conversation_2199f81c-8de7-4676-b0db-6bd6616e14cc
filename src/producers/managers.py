import sys
import typing

from django.db import models
from django.db.models import (
    Count,
    Prefetch,
    QuerySet,
    query,
)

from safedelete.managers import SafeDeleteManager
from safedelete.queryset import SafeDeleteQueryset

from orders.enums import OrderType
from producers.choices import (
    BatchType,
    ProductPriority,
    ProductStatus,
)

if typing.TYPE_CHECKING:
    from producers.models import (
        BatchingSettings,
        Product,
        ProductBatch,
        QualityHoldReleaseRequest,
    )


class ProductQuerySet(SafeDeleteQueryset):
    def with_latest_status_history(self, statuses):
        from producers.models import ProductStatusHistory

        return self.prefetch_related(
            Prefetch(
                'product_status_history',
                ProductStatusHistory.objects.filter(status__in=statuses).order_by(
                    '-changed_at'
                ),
                to_attr='latest_status_history',
            )
        )


class ProductPriorityHistoryQuerySet(models.QuerySet):
    def exclude_first_on_hold(self):
        first_on_hold = (
            self.filter(priority=ProductPriority.ON_HOLD).order_by('-id').first()
        )
        if not first_on_hold:
            return self
        return self.exclude(pk=first_on_hold.pk)


class ProductQualityQueryset(query.QuerySet):
    def with_review(self):
        from reviews.models import Review

        return self.prefetch_related(
            Prefetch(
                'order__review_set',
                Review.objects.all(),
                to_attr='latest_review',
            )
        )

    def with_complaints(self):
        from complaints.models import Complaint

        return self.prefetch_related(
            Prefetch(
                'complaint_set',
                Complaint.objects.all(),
                to_attr='latest_complaint',
            )
        )

    def with_reproduction_products(self):
        from producers.models import Product

        return self.prefetch_related(
            Prefetch(
                'reproduction_products',
                Product.objects.all(),
                to_attr='latest_reproduction_products',
            )
        )

    def with_reproducton_complaints_count(self):
        return self.annotate(complaint_count=Count('complaint'))


class ProductManager(SafeDeleteManager):
    _queryset_class = ProductQuerySet

    def ready_for_batching(self, product_ids: list[int]) -> QuerySet['Product']:
        from producers.models import Product

        queryset = self.get_queryset()
        return (
            queryset.filter(
                id__in=product_ids,
                batch__isnull=True,
                cached_product_type__in=Product.JETTY_WATTY,
            )
            .exclude(priority__in=ProductPriority.on_hold_and_postponed_priorities())
            .exclude(status__in=[ProductStatus.ABORTED, ProductStatus.INTERNAL_USAGE])
            .exclude(copy_of__isnull=False)
            .select_related('order')
            .prefetch_related('order_item__order_item')
            .order_by('id')
        )


class ProductComplaintManager(SafeDeleteManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(order__order_type=OrderType.COMPLAINT)
            .prefetch_related('reproduction_complaints')
        )


class ProductBatchComplaintManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(batch_type=BatchType.COMPLAINTS)


class ProductQualityManager(models.Manager):
    _queryset_class = ProductQualityQueryset

    def get_queryset(self):
        qs = (
            super()
            .get_queryset()
            .select_related(
                'manufactor',
                'order',
                'batch',
                'batch__manufactor',
                'order_item',
                'order__owner',
                'product_details_jetty',
                'product_details_watty',
                'order_item__free_return',
            )
            .prefetch_related(
                'order_item__order_item',
                'reproduction_complaints',
                'reproduction_products',
                'order__review_set',
            )
            .exclude(order__order_type=OrderType.COMPLAINT)
            .with_review()
            .with_complaints()
            .with_reproduction_products()
            .with_reproducton_complaints_count()
        )
        return qs


class BatchingSettingsManager(models.Manager):
    def get_or_return_default(
        self, manufactor_id: int, shelf_type: int
    ) -> 'BatchingSettings':
        """
        Get batching settings for a specific manufactor.
        If not found, return default settings.
        """
        from producers.models import BatchingSettings

        batch_settings = (
            self.filter(
                manufactor_id=manufactor_id,
                shelf_type=shelf_type,
            )
            .select_related('manufactor')
            .first()
        )
        if batch_settings:
            return batch_settings
        return BatchingSettings(
            separate_desks=False,
            min_size=0,
            max_size=25,
            max_area=sys.maxsize,
        )


class QualityHoldReleaseRequestManager(models.Manager['QualityHoldReleaseRequest']):
    def pending_quality_hold_requests(
        self, batch: 'ProductBatch | None' = None
    ) -> QuerySet['QualityHoldReleaseRequest']:

        queryset = self.filter(accepted_at__isnull=True, rejected_at__isnull=True)
        if batch:
            queryset = queryset.filter(batch=batch)
        return queryset
