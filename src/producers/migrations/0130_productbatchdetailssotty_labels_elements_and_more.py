# Generated by Django 4.2.23 on 2025-07-09 20:15

import django.core.files.storage

from django.db import (
    migrations,
    models,
)

import producers.utils


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0129_product_cover_only'),
    ]

    operations = [
        migrations.AddField(
            model_name='productbatchdetailssotty',
            name='labels_elements',
            field=models.FileField(
                blank=True,
                max_length=400,
                null=True,
                storage=django.core.files.storage.FileSystemStorage(),
                upload_to=producers.utils.RandomizedUploadTo(
                    'producers/batch/labels_elements/%Y/%m'
                ),
                verbose_name='Etykiety elementy',
            ),
        ),
        migrations.AddField(
            model_name='productbatchdetailssotty',
            name='labels_packaging',
            field=models.FileField(
                blank=True,
                max_length=400,
                null=True,
                storage=django.core.files.storage.FileSystemStorage(),
                upload_to=producers.utils.RandomizedUploadTo(
                    'producers/batch/labels_packaging/%Y/%m'
                ),
                verbose_name='Etykie<PERSON> pakowanie',
            ),
        ),
    ]
