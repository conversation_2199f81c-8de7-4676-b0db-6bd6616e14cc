from django.db import models


class QualityPriorityChoices(models.IntegerChoices):
    PRIORITY_1 = (1, '1')
    PRIORITY_2 = (2, '2')
    PRIORITY_3 = (3, '3')
    PRIORITY_OTHER = (11, 'Other')


class QualityResultChoices(models.IntegerChoices):
    OK = 1
    INFORMATION_DEFECT = 2
    MAIN_DEFECT = 3
    CRITICAL_DEFECT = 4


class ProductStatus(models.IntegerChoices):
    ABORTED = 0, 'Aborted'
    NEW = 1, 'Accepted to production'
    UTILIZATION = 2, 'Utilization'
    UTILIZATION_DONE = 3, 'Utilization Done'
    ASSIGNED_TO_PRODUCTION = 4, 'Sent to production'
    IN_PRODUCTION = 5, 'In production'
    SENT_TO_CUSTOMER = 6, 'Sent to customer'
    SENT_TO_WAREHOUSE = 7, 'Finished product'
    QUALITY_BLOCKER = 8, 'Quality Blocker'
    INTERNAL_USAGE = 9, 'Internal use'
    TO_BE_SHIPPED = 10, 'To be shipped'
    QUALITY_CONTROL = 11, 'Quality Control'
    ABORTED_DONE = 12, 'Aborted done'  # product was aborted, but produced anyway
    SHELFMARKET = 13, 'Shelfmarket'
    SHELFMARKET_DONE = 14, 'Shelfmarket Done'
    DELIVERED_TO_CUSTOMER = 15, 'Delivered to Customer'
    CM_UTILIZATION_DONE = 16, 'CM Utilization Done'
    TO_BE_SHIPPED_AWIZATION = 17, 'To be shipped awization'
    INTERNAL_SHIPMENT = 18, 'Internal Shipment'

    @classmethod
    def statuses_for_admin_filter(cls) -> list:
        return [
            (cls.ABORTED, cls.ABORTED.label),
            (cls.NEW, cls.NEW.label),
            (cls.UTILIZATION, cls.UTILIZATION.label),
            (cls.UTILIZATION_DONE, cls.UTILIZATION_DONE.label),
            (cls.ASSIGNED_TO_PRODUCTION, cls.ASSIGNED_TO_PRODUCTION.label),
            (cls.IN_PRODUCTION, cls.IN_PRODUCTION.label),
            (cls.SENT_TO_CUSTOMER, cls.SENT_TO_CUSTOMER.label),
            (cls.SENT_TO_WAREHOUSE, cls.SENT_TO_WAREHOUSE.label),
            (cls.QUALITY_BLOCKER, cls.QUALITY_BLOCKER.label),
            (cls.INTERNAL_USAGE, cls.INTERNAL_USAGE.label),
            (cls.TO_BE_SHIPPED, cls.TO_BE_SHIPPED.label),
            (cls.QUALITY_CONTROL, cls.QUALITY_CONTROL.label),
            (cls.ABORTED_DONE, cls.ABORTED_DONE.label),
            (cls.SHELFMARKET, cls.SHELFMARKET.label),
            (cls.SHELFMARKET_DONE, cls.SHELFMARKET_DONE.label),
            (cls.DELIVERED_TO_CUSTOMER, cls.DELIVERED_TO_CUSTOMER.label),
            (cls.CM_UTILIZATION_DONE, cls.CM_UTILIZATION_DONE.label),
            (cls.TO_BE_SHIPPED_AWIZATION, cls.TO_BE_SHIPPED_AWIZATION.label),
            (cls.INTERNAL_SHIPMENT, cls.INTERNAL_SHIPMENT.label),
        ]

    @classmethod
    def not_ready(cls) -> set:
        return {
            cls.NEW,
            cls.ASSIGNED_TO_PRODUCTION,
            cls.IN_PRODUCTION,
            cls.QUALITY_CONTROL,
        }

    @classmethod
    def in_quality_check(cls) -> set:
        return {
            cls.QUALITY_BLOCKER,
            cls.QUALITY_CONTROL,
        }

    @classmethod
    def with_logistic_split_required(cls) -> set:
        return {
            cls.UTILIZATION,
            cls.UTILIZATION_DONE,
            cls.SHELFMARKET,
            cls.SHELFMARKET_DONE,
            cls.CM_UTILIZATION_DONE,
        }


class ProductPriority(models.IntegerChoices):
    ON_HOLD = 1
    POSTPONED = 2
    NORMAL = 5, ''
    IMPORTANT = 10
    FAST_TRACK = 90
    ESTIMATED_DELIVERY_DATE = 110, 'EDD'
    ON_HOLD_DES = 120, 'On hold DES'
    # moved to ProductionPriority, but stay here because of ProductPriorityHistory
    B2B = 15
    INFLU = 17, 'INFLU'
    VIP = 20, 'VIP'
    R_AND_D = 60, 'R&D'
    BIG_ORDERS = 70
    # moved to DeliveryPriority, but stay here because of ProductPriorityHistory
    ASSEMBLY = 80

    def is_prioritized(self):
        return self in {self.IMPORTANT, self.FAST_TRACK, self.ESTIMATED_DELIVERY_DATE}

    @classmethod
    def on_hold_switch_label(cls):
        return 'On hold - switch'

    @classmethod
    def on_hold_priorities(cls) -> set:
        return {cls.ON_HOLD, cls.ON_HOLD_DES}

    @property
    def label_with_normal_displayed(self):
        if self.value == self.NORMAL:
            return 'Normal'
        return self.label

    @classmethod
    def on_hold_and_postponed_priorities(cls) -> set:
        return cls.on_hold_priorities() | {cls.POSTPONED}

    @classmethod
    def choices_available_for_users(cls) -> list:
        choices = cls.choices
        return [
            choice for choice in choices if choice[0] not in cls.deprecated_choices()
        ]

    @classmethod
    def deprecated_choices(cls):
        return {
            cls.B2B,
            cls.INFLU,
            cls.VIP,
            cls.R_AND_D,
            cls.BIG_ORDERS,
            cls.ASSEMBLY,
        }


class DeliveryPriority(models.IntegerChoices):
    NORMAL = 0, ''
    ASSEMBLY = 5
    NOT_DELIVERED = 10


class SourcePriority(models.IntegerChoices):
    VIP = 1
    B2B = 5
    INFLU = 10
    R_AND_D = 15, 'R&D'
    BIG_ORDER = 20
    PROD = 25
    NORMAL = 30, ''


class AccessLevel(models.Choices):
    FULL_ACCESS = 'biuro'
    WAREHOUSE = 'magazyn'
    PRODUCTION = 'produkcja'


class BatchStatus(models.IntegerChoices):
    ABORTED = 0, 'Aborted'
    NEW = 1, 'Waiting for nesting'
    IN_PRODUCTION = 2, 'In production'
    SENT_TO_CUSTOMER = 3, 'Sent to customer'


class BatchNeededActions(models.IntegerChoices):
    NO_ACTION_NEEDED = 0, 'no action needed'
    CALCULATION_IN_PROGRESS = 3, 'calculation in progress'
    RECALCULATE_FILES = 5, 'recalculate files'
    FIX_ERRORS = 10, 'fix errors'


class BatchType(models.IntegerChoices):
    NOT_SET = 0, 'NOT SET'
    STANDARD = 1, 'STANDARD'
    EXTENDED = 2, 'EXTENDED'
    CUSTOM = 3, 'CUSTOM'
    DRAWERS = 4, 'DRAWERS'
    DRAWERS_EXTENDED = 5, 'DRAWERS EXTENDED'
    COMPLAINTS = 10, 'COMPLAINTS'


class ProductionFileStatus(models.IntegerChoices):
    INITIAL = 0, 'initial'
    DOWNLOADED = 1, 'downloaded'
    RECALCULATED_AFTER_DOWNLOAD = 2, 'recalculated after download'
