import logging

from collections import defaultdict

from django.contrib import (
    admin,
    messages,
)
from django.shortcuts import redirect
from django.urls import (
    path,
    reverse,
)
from django.utils.html import (
    format_html,
    format_html_join,
)
from django.utils.safestring import mark_safe

from rangefilter.filters import DateRangeFilter

from admin_customization.admin import ButtonActionsBaseAdmin
from automatic_batching.tasks import (
    create_complaint_batches,
    send_auto_email_with_files_to_manufactor,
)
from custom.admin_action import admin_action_with_form
from custom.enums import ShelfType
from custom.filters import (
    ExpressReplacementFilter,
    IsBatchedFilter,
)
from custom.utils.mixins import ReadOnlyAdminMixin
from logger.models import Log
from orders.enums import (
    OrderStatus,
    OrderType,
)
from producers import (
    admin_actions,
    constants,
)
from producers.admin_actions import (
    batching_choose_manufactor,
    batching_confirm_batches,
)
from producers.admin_column_mixins import ProductAdminGetItemColorMixin
from producers.admin_filters import (
    AcceptedToProductionFilter,
    <PERSON><PERSON>t<PERSON><PERSON><PERSON>,
    DnaStyleFilter,
    EstimatedDeliveryDateFilter,
    IsRequestAcceptedFilter,
    IsRequestRejectedFilter,
    MultipleChoiceDeliveryPriorityFilter,
    MultipleChoiceListCustomProductBatch,
    MultipleChoiceListCustomProductInProductionFilter,
    MultipleChoicePriorityFilter,
    MultipleChoiceProductComplaintReproductionElementsFilter,
    MultipleChoiceSourcePriorityFilter,
    MultipleColoursProductBatchComplaintFilter,
    ProductBatchComplaintManufactorFaultFilter,
    ProductBatchingFilter,
    ProductComplaintManufactorFaultFilter,
    ProductDescriptionFilter,
    ProductFeaturesFilter,
    ProductQualityComplaintAssemblyServiceFilter,
    ProductQualityHasComplaintFilter,
    ProductQualityHasFreeReturnFilter,
    ProductQualityReviewScoreFilter,
    ProductStatusFilter,
    ReproductionTimeFilterProductBatch,
    ShelfTypeFilterProduct,
    ShelfTypeFilterProductBatch,
    ToBeShippedFilter,
    VerticalHeightsFilter,
    W3DrawerTypeFilter,
    WithoutDeliveryDateFilter,
    WithoutProductionFilesFilter,
)
from producers.admin_forms import AutoBatchingMailingConfigurationForm
from producers.admin_inlines import (
    ProductBatchDetailsJettyInline,
    ProductBatchDetailsSottyInline,
    ProductBatchDetailsWattyInline,
    ProductDetailsJettyInline,
    ProductDetailsSottyInline,
    ProductDetailsWattyInline,
    ProductPriorityHistoryInline,
    ProductStatusHistoryInline,
)
from producers.admin_mixins import (
    ProductAdminQuerySetMixin,
    ProductBatchAdminQuerySetMixin,
    ProductDetailsFilesChangelogMixin,
)
from producers.choices import (
    BatchType,
    DeliveryPriority,
    ProductStatus,
    SourcePriority,
)
from producers.errors import SerializationMissingError
from producers.forms import (
    ChangeDeliveryPriorityForm,
    ChangeSourcePriorityForm,
)
from producers.importers import ProductEstimatedProductionDateImporter
from producers.models import (
    BatchingSettings,
    Manufactor,
    ManufactorAdditionalAccounts,
    ManufacturerReleasedPackage,
    Product,
    ProductAborted,
    ProductBatch,
    ProductBatchComplaint,
    ProductComplaint,
    QualityHoldReleaseRequest,
)
from producers.models_split.batch_mailing import (
    AutoBatchingMailingConfiguration,
    BatchMailingStatus,
)
from producers.models_split.product_details import (
    ProductDetailsJetty,
    ProductDetailsSotty,
    ProductDetailsWatty,
)
from producers.models_split.product_quality import ProductQuality
from producers.production_system_utils.enums import PSConnectionSettings
from producers.utils import (
    format_isoformat_date_time_to_date,
    get_display_front_view_link,
)
from production_margins.admin_mixins import CSVImportActionMixin

logger = logging.getLogger('producers')


@mark_safe
def get_pdf_for_packaging(obj, complaints=False):
    result = ''
    if obj.product_type in Product.JETTY_WATTY:
        if not complaints:
            result += (
                f'<a href="/admin/ivy_packaging_pdf/{obj.id}/?dxf=true">'
                f'Get packaging dxf</a><br/>'
            )
        result += f'<a href="/admin/ivy_front_dxf/{obj.id}/">Get frontview dxf</a>'
        gcode_view = PSConnectionSettings.GCODE_VIEW.format(product_id=obj.id)
        result += f'<br/><a href="{gcode_view}">Display gcode</a>'
    if obj.product_type in Product.PS_SERIALIZED:
        front_view_link = get_display_front_view_link(obj)
        result += f'<br/><a href="{front_view_link}">Display front view</a>'
        result += (
            f'<br/><a href="/admin/json_production/{obj.id}/">' 'Display saved ser</a>'
        )
        result += (
            f'<br/><a href="/admin/json_production/{obj.id}/recalculate/">'
            'Display recalc ser</a>'
        )
    product_details_url = None
    if obj.product_type == 'jetty':
        product_details_url = reverse(
            'admin:producers_productdetailsjetty_change',
            args=[obj.product_details_jetty.id],
        )
    elif obj.product_type == 'watty':
        product_details_url = reverse(
            'admin:producers_productdetailswatty_change',
            args=[obj.product_details_watty.id],
        )
    elif obj.product_type == 'sotty':
        product_details_url = reverse(
            'admin:producers_productdetailssotty_change',
            args=[obj.product_details_sotty.id],
        )
    if product_details_url:
        result += format_html(
            '<br/><a href="{}">Production Files</a>',
            product_details_url,
        )
    return result


get_pdf_for_packaging.short_description = 'Tools for customization'


class ProductAdmin(
    ProductAdminQuerySetMixin,
    ProductAdminGetItemColorMixin,
    CSVImportActionMixin,
    ReadOnlyAdminMixin,
    ButtonActionsBaseAdmin,
):
    model = Product

    list_max_show_all = 800
    list_per_page = 50

    list_display = (
        'id',
        'logistic_order',
        'manufactor',
        'get_elements_order',
        'batch_link',
        'source_priority',
        'get_priority_display_with_switched_on_hold',
        'delivery_priority',
        'quality_control_needed',
        'get_item_color',
        'get_order_id',
        'get_order_customer_as_string',
        'get_order_country_as_string',
        'status',
        'shelf_type',
        'get_serialization_status',
        'created_at',
        'updated_at',
        'estimated_order_production_date',
        'estimated_production_date',
        'get_cached_product_type',
        'check_for_instruction',
        'check_for_production_files',
        get_pdf_for_packaging,
    )

    readonly_fields = (
        'order_link',
        'order_item_link',
        'order_item_serialized',
        'furniture_link',
        'copy_of_link',
        'status',
        'previous_status',
        'delivered_at',
        'estimated_delivery_time_log',
        'related_logs',
    )

    autocomplete_fields = ('batch',)

    fieldsets = (
        (
            'Related objects',
            {
                'fields': (
                    'order_link',
                    'order_item_link',
                    'copy_of_link',
                    'furniture_link',
                ),
            },
        ),
        (
            'Order serialized (click to expand)',
            {
                'fields': ('order_item_serialized',),
            },
        ),
        (
            'Product',
            {
                'fields': (
                    'manufactor',
                    'warehouse',
                    'batch',
                    'cached_product_type',
                    'display_notes_on_front_view',
                    'notes',
                    'priority',
                    'source_priority',
                    'delivery_priority',
                    'quality_control_needed',
                    'delivered_at',
                    'status',
                    'previous_status',
                    'gala_status',
                    'estimated_delivery_time_log',
                    'estimated_production_date',
                )
            },
        ),
        (
            'Logs',
            {
                'fields': ('related_logs',),
            },
        ),
    )

    list_display_links = (
        'id',
        'manufactor',
    )

    list_filter = (
        ('order__estimated_delivery_time', DateRangeFilter),
        ProductBatchingFilter,
        ProductStatusFilter,
        'cached_configurator_type',
        'cached_physical_product_version',
        'order__assembly',
        MultipleChoicePriorityFilter,
        MultipleChoiceSourcePriorityFilter,
        MultipleChoiceDeliveryPriorityFilter,
        'quality_control_needed',
        'order__returning_client',
        ToBeShippedFilter,
        AcceptedToProductionFilter,
        ShelfTypeFilterProduct,
        MultipleChoiceListCustomProductInProductionFilter,
        CreatedAtFilter,
        'manufactor',
        DnaStyleFilter,
        ProductDescriptionFilter,
        'cached_product_type',
        WithoutDeliveryDateFilter,
        EstimatedDeliveryDateFilter,
        WithoutProductionFilesFilter,
        ProductFeaturesFilter,
        W3DrawerTypeFilter,
        VerticalHeightsFilter,
    )

    search_fields = (
        'id',
        'order__id',
    )

    raw_id_fields = ('copy_of',)

    button_actions = ('import_estimated_production_date_from_csv',)
    list_select_related = ('order',)

    actions = [
        'batching_wizard',
        admin_actions.generate_instruction_anonymous,
        admin_actions.generate_instruction,
        admin_actions.batch_with_manufactor,  # disable on prod?
        admin_actions.change_status_with_history,
        admin_actions.admin_change_priority_for_products,
        'change_source_priority',
        'change_delivery_priority',
        admin_actions.export_files_as_zip,
        admin_actions.front_views_package_recalculate,
        admin_actions.packaging_instruction_package_recalculate,
        admin_actions.front_connection_and_packaging_recalculate,
        admin_actions.recalc_serialization_using_ps,
        admin_actions.create_connections,
        admin_actions.ivy_product_usage,
        admin_actions.get_filtered_batching_tool,
        admin_actions.export_for_production_timeline_product,
        admin_actions.export_for_production_timeline_with_labels,
        admin_actions.get_elements_csv_meble_pl,
        admin_actions.get_cardboard_merged_file_meblepl,
        admin_actions.get_cardboard_merged_file_drewtur,
        admin_actions.send_email_production_report,
        admin_actions.send_last_quarter_orders_from_france_report,
        admin_actions.fetch_production_drawings_from_ps,
        admin_actions.export_selected_products_for_file,
        admin_actions.update_cached_features,
        admin_actions.change_quality_control_needed,
        admin_actions.change_status_to_quality_blocker,
        admin_actions.get_order_summary_xls_for_products_by_batches,
        admin_actions.get_order_summary_xls_for_products,
        'get_ship_in_date',
        admin_actions.generate_t13_door_handling_order_products_report_action,
        admin_actions.generate_t25_legs_report_products,
        admin_actions.remove_products_from_batch,
        admin_actions.trigger_proposed_delivery_report,
        admin_actions.gala_batching_tool,
        admin_actions.generate_delivered_packages_report,
        admin_actions.generate_delivered_products_report,
    ]

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                'batching/choose_manufactor/',
                self.admin_site.admin_view(batching_choose_manufactor),
                name='batching_choose_manufactor',
            ),
            path(
                'batching/confirm_batches/',
                self.admin_site.admin_view(batching_confirm_batches),
                name='batching_confirm_batches',
            ),
        ]
        return custom_urls + urls

    @admin.action(description='Batching Wizard')
    def batching_wizard(self, request, queryset):
        product_ids = list(queryset.values_list('id', flat=True))
        request.session['product_ids'] = product_ids
        return redirect('batching/choose_manufactor/')

    def related_logs(self, obj):
        logs = Log.objects.filter(model=Product._meta.label, model_id=obj.id).order_by(
            '-date'
        )
        if not logs.exists():
            return ''

        table_data = [
            (
                log.user_id,
                log.action,
                str(log.data)[:40] if log.data else '',
                log.date,
            )
            for log in logs
        ]
        table = format_html_join(
            '',
            '<tr><td>{}</td><td>{}</td><td>{}</td><td>{}</td></tr>',
            table_data,
        )
        return format_html('<table>{}</table>', table)

    @admin.action(description='Import Estimated Production Date from CSV')
    def import_estimated_production_date_from_csv(self, request):
        return self.import_csv_file_action(
            request,
            ProductEstimatedProductionDateImporter,
        )

    @admin.display(description='Order')
    def order_link(self, obj):
        link = reverse('admin:orders_order_change', args=[obj.order_id])
        return format_html('<a href="{}">{}</a>', link, obj.order)

    @admin.display(description='Order item')
    def order_item_link(self, obj):
        link = reverse('admin:orders_orderitem_change', args=[obj.order_item_id])
        return format_html('<a href="{}">{}</a>', link, obj.order_item)

    @admin.display(description='Original product')
    def copy_of_link(self, obj):
        link = reverse('admin:producers_product_change', args=[obj.copy_of_id])
        return format_html('<a href="{}">{}</a>', link, obj.copy_of)

    @admin.display(description='Gallery Jetty/Watty')
    def furniture_link(self, obj):
        product_type = obj.order_item.order_item.product_type
        link = reverse(
            f'admin:gallery_{product_type}_change',
            args=[obj.order_item.order_item.id],
        )
        link_list = reverse(
            f'admin:gallery_{product_type}_changelist',
        )
        return format_html(
            '<a href="{}">{}</a><br><a href="{}?q={}">{} list (filtered)</a>',
            link,
            obj.order_item.order_item,
            link_list,
            obj.order_item.order_item.id,
            product_type.capitalize(),
        )

    def get_inlines(self, request, obj):
        inlines = []
        if obj.product_type == 'jetty':
            inlines.append(ProductDetailsJettyInline)
        if obj.product_type == 'watty':
            inlines.append(ProductDetailsWattyInline)
        if obj.product_type == Product.SOTTY:
            inlines.append(ProductDetailsSottyInline)
        # we want history after the details
        inlines.extend([ProductStatusHistoryInline, ProductPriorityHistoryInline])
        return inlines

    def get_search_results(self, request, queryset, search_term):
        if search_term.startswith('*'):
            search_term = search_term[1:]
            return queryset.filter(batch_id__in=search_term.split(';')), False
        return super().get_search_results(request, queryset, search_term)

    def save_model(self, request, obj, form, change):
        if change and 'priority' in form.changed_data:
            previous_priority = self.model.objects.get(pk=obj.pk).priority
            obj.priority_updater.change_priority(
                form.cleaned_data['priority'], previous_priority, owner=request.user
            )
        if change and 'delivery_priority' in form.changed_data:
            previous_priority = self.model.objects.get(pk=obj.pk).delivery_priority
            obj.priority_updater.change_delivery_priority(
                DeliveryPriority(form.cleaned_data['delivery_priority']),
                request.user,
                DeliveryPriority(previous_priority),
            )
        if change and 'source_priority' in form.changed_data:
            previous_priority = self.model.objects.get(pk=obj.pk).source_priority
            obj.priority_updater.change_source_priority(
                SourcePriority(form.cleaned_data['source_priority']),
                request.user,
                SourcePriority(previous_priority),
            )
        super().save_model(request, obj, form, change)

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False

    @admin.display(description='Elements Order')
    def get_elements_order(self, product):
        if product.batch:
            elements_orders = product.batch.elementsorders
            if elements_orders:
                return format_html(
                    f'<a href="{elements_orders[0].order_file.url}">'
                    f'{elements_orders[0].order_file.name}</a>',
                )
        return ''

    @admin.display(description='Batch')
    def batch_link(self, product):
        """Return link to batch on batchlist."""
        if product.batch_id:
            batches_page_name = (
                'productbatchcomplaint'
                if product.batch.batch_type == BatchType.COMPLAINTS
                else 'productbatch'
            )
            return format_html(
                '<a href="{}?id__exact={}">{}</a>',
                reverse(f'admin:producers_{batches_page_name}_changelist'),
                product.batch_id,
                product.batch,
            )
        return ''

    @admin.display(description='Shelf type')
    def shelf_type(self, obj):
        return format_html(obj.get_shelf_type(with_configurator_type=True))

    @admin.display(description='Order Estimated Production Date')
    def estimated_order_production_date(self, obj):
        return obj.order.estimated_delivery_time

    @admin.display(description='Serialized at')
    def get_serialization_status(self, obj):
        if obj.product_type in Product.PS_SERIALIZED:
            error_template = '<p style="background: red">{message}: {error}</p>'
            try:
                jetty_serialized = obj.get_jetty_serialized()
            except (ValueError, TypeError) as ugly_error:
                return format_html(
                    error_template,
                    message='UGLY ERROR, CALL PARAMETRIC TEAM',
                    error=ugly_error,
                )
            except SerializationMissingError:
                return format_html(
                    error_template,
                    message='Missing serialization',
                    error='try to refresh from production system.',
                )
            error_message = jetty_serialized.error_message or []
            id_manufactor = jetty_serialized.id_manufactor
            version = jetty_serialized.serialization_version
            serialized_at = jetty_serialized.serialization_date

            return format_html(
                '''{div}
                  <table>
                    <tbody>
                      <tr {red_class}>
                        <td><b>{manufactor}</b></td>
                        <td>manufactor</td>
                      </tr>
                      <tr {red_class}>
                        <td><b>{version}</b></td>
                        <td>version</td>
                      </tr>
                    </tbody>
                  </table>
                  <b>{serialized_at}</b>{errors}
                </div>
                ''',
                div=(
                    format_html('<div class="tooltip" style="background: red">')
                    if error_message
                    else ''
                ),
                manufactor=id_manufactor,
                version=version,
                serialized_at=serialized_at,
                errors=format_html(
                    "<span class='tooltiptext'>{}</span></div>",
                    '<br/>'.join([str(e) for e in error_message]),
                ),
                red_class=(
                    format_html('style="background: red"') if error_message else ''
                ),
            )
        else:
            return ''

    @admin.display(description='Instruction?')
    def check_for_instruction(self, obj):
        if obj.product_type in Product.JETTY_WATTY:
            circle_color = 'green' if obj.details.instruction else 'red'
            return format_html(
                "<i class='circle-with-background {}'> </i>",
                circle_color,
            )
        return ''

    @admin.display(description='Files?')
    def check_for_production_files(self, obj):
        if obj.product_type in Product.PS_SERIALIZED:
            return format_html(
                "<i class='circle-with-background {color}'></i>",
                color=obj.details.combined_files_status.color,
            )
        return ''

    @admin.display(description='Order Estimated Delivery Time History')
    def estimated_delivery_time_log(self, obj):
        if not obj.order.estimated_delivery_time_log:
            return '-'

        estimated_delivery_dates = (
            (format_isoformat_date_time_to_date(delivery_time),)
            for delivery_time in obj.order.estimated_delivery_time_log
        )

        return format_html_join('\n', '<div>{}</div>', estimated_delivery_dates)

    @admin.display(description='Change source priority')
    def change_source_priority(self, request, queryset):
        def change_priority_success(modeladmin, request, queryset, form, **kwargs):
            user = request.user
            new_priority = form.cleaned_data.get('priority')
            for product in queryset:
                product.priority_updater.change_source_priority(
                    SourcePriority(new_priority), user
                )
            modeladmin.message_user(request, 'Priority changed successfully')

        return admin_action_with_form(
            modeladmin=self,
            request=request,
            queryset=queryset,
            form_class=ChangeSourcePriorityForm,
            success_function=change_priority_success,
            success_function_kwargs={},
        )

    @admin.display(description='Change delivery priority')
    def change_delivery_priority(self, request, queryset):
        def change_priority_success(modeladmin, request, queryset, form, **kwargs):
            new_priority = form.cleaned_data.get('priority')
            for product in queryset:
                product.priority_updater.change_delivery_priority(
                    DeliveryPriority(new_priority), request.user
                )
            modeladmin.message_user(request, 'Delivery priority changed successfully')

        return admin_action_with_form(
            modeladmin=self,
            request=request,
            queryset=queryset,
            form_class=ChangeDeliveryPriorityForm,
            success_function=change_priority_success,
            success_function_kwargs={},
        )


class ManufactorAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        'id',
        'name',
        'owner',
        'created_at',
        'get_orders_assigned',
        'get_orders_in_production',
        'get_orders_sent_to_customer',
        'get_orders_aborted',
    )
    list_display_links = ('id', 'name')
    raw_id_fields = ('owner',)

    def get_queryset(self, request):
        qs = self.model.all_objects.get_queryset()
        return qs

    @admin.display()
    def get_orders_assigned(self, obj):
        return obj.products.filter(status=ProductStatus.ASSIGNED_TO_PRODUCTION).count()

    @admin.display()
    def get_orders_in_production(self, obj):
        return obj.products.filter(status=ProductStatus.IN_PRODUCTION).count()

    @admin.display()
    def get_orders_sent_to_customer(self, obj):
        return obj.products.filter(status=ProductStatus.SENT_TO_CUSTOMER).count()

    @admin.display()
    def get_orders_aborted(self, obj):
        return obj.products.filter(status=ProductStatus.ABORTED).count()


class ProductBatchAdmin(
    ProductBatchAdminQuerySetMixin,
    ReadOnlyAdminMixin,
    admin.ModelAdmin,
):
    actions = (
        admin_actions.generate_instructions_for_selected_batches,
        admin_actions.sum_usage_for_all_items_from_batch,
        admin_actions.sum_usage_for_all_items_from_batch_async,
        admin_actions.sum_usage_for_all_items_from_batch_telmex_async,
        admin_actions.sum_usage_for_all_items_from_batch_vertical,
        admin_actions.export_for_production_timeline,
        admin_actions.export_for_production_timeline_with_labels,
        admin_actions.recalculate_batch_products_serialization_using_ps,
        admin_actions.recalculate_all_batch_files,
        admin_actions.recalculate_batch_nesting_files,
        admin_actions.check_for_all_files_manually,
        admin_actions.generate_meblepl_package,
        admin_actions.get_cardboard_merged_file,
        admin_actions.get_order_summary_xls,
        admin_actions.get_usage_in_xml,
        admin_actions.get_usage_in_xml_with_match,
        admin_actions.get_speedtrans_order_type01_doors,
        admin_actions.get_speedtrans_order_type01_drawers,
        admin_actions.get_speedtrans_order_type02,
        admin_actions.download_nesting_files,
        admin_actions.create_zip_for_all_items_from_batch,
        admin_actions.get_meblepl_zips,
        admin_actions.generate_t13_door_handling_order_batches_report_action,
        admin_actions.generate_t25_legs_report_batches,
        admin_actions.lock_selected_batches,
        admin_actions.upload_batches_to_meblepl,
        admin_actions.remove_all_products_from_batches,
        admin_actions.get_nameplate_files,
        admin_actions.get_flipper_count,
        admin_actions.send_email_with_reports_to_producers,
        admin_actions.create_carton_files_for_s93,
        admin_actions.create_cartoner_file_for_batches,
        admin_actions.generate_spacers_report,
    )
    list_display = (
        'id',
        'elements_order',
        'manufactor',
        'status',
        'locked_at',
        'actions_needed',
        'product_type',
        'is_meblepl_upload_confirmed',
        'check_for_instruction',
        'check_for_production_files',
        'check_for_labels',
        'get_batch_items_number_and_link',
        'get_batch_type',
        'check_for_customs',
        'created_at',
        'processing_started_at',
        'planed_ready_at',
        'picked_up_at',
        'batch_type',
        'material_description',
        'batch_value',
        'auto_mail_was_sent',
    )
    change_form_template = 'admin/producers/batch/change_form.html'
    readonly_fields = ['locked_at']
    search_fields = ('id',)
    list_filter = (
        ('created_at', DateRangeFilter),
        'status',
        'locked_at',
        'actions_needed',
        'manufactor',
        'batch_type',
        MultipleChoiceListCustomProductBatch,
        ShelfTypeFilterProductBatch,
    )

    def get_inlines(self, request, obj):
        if obj.product_type == 'jetty':
            return (ProductBatchDetailsJettyInline,)
        if obj.product_type == 'watty':
            return (ProductBatchDetailsWattyInline,)
        if obj.product_type == 'sotty':
            return (ProductBatchDetailsSottyInline,)
        return ()

    def get_batch_items_number_and_link(self, batch, complaint=False):
        if complaint:
            redirect_url = reverse('admin:producers_productcomplaint_changelist')
        else:
            redirect_url = reverse('admin:producers_product_changelist')
        return format_html(
            '<a href="{}?batch__id__exact={}">{}</a>',
            redirect_url,
            batch.id,
            batch.get_batch_items_number(),
        )

    get_batch_items_number_and_link.short_description = 'Items'

    @mark_safe
    def check_for_instruction(self, obj):
        if obj.product_type in Product.JETTY_WATTY:
            check = [
                product.details.instruction not in {None, ''}
                for product in obj.batch_items.all()
            ]
            if all(check):
                return "<i class='circle-with-background green'> </i>"
            if any(check):
                return "<i class='circle-with-background yellow'> </i>"
            if len(check) > 0:
                return "<i class='circle-with-background red'> </i>"
            else:
                return "<i class='circle-with-background'>?</i>"
        else:
            return ''

    check_for_instruction.short_description = 'Instruction?'

    def check_for_production_files(self, obj):
        if obj.product_type in Product.JETTY_WATTY:
            return format_html(
                "<i class='circle-with-background {color}'></i>",
                color=obj.details.combined_files_status.color,
            )
        return ''

    check_for_production_files.short_description = 'Files?'

    @admin.display(description='Elem Order')
    def elements_order(self, obj):
        if obj.elementsorders:
            elements_order = [x for x in obj.elementsorders if x.is_standard_type]
            return elements_order[0] if elements_order else None

    @mark_safe
    def check_for_labels(self, obj):
        if obj.product_type in Product.JETTY_WATTY:
            labels_files = {
                'labels_elements',
                'labels_packaging',
                'labels_logistic',
            }
            if obj.product_type == 'jetty':
                labels_files.add('labels_verticals')
            if obj.product_type == 'watty':
                labels_files.add('labels_adapters')
            labels_status = max(
                obj.details.get_file_status(file_field) for file_field in labels_files
            )
            return format_html(
                "<i class='circle-with-background {color}'></i>",
                color=labels_status.color,
            )
        else:
            return ''

    check_for_labels.short_description = 'Labels?'

    @mark_safe
    def check_for_customs(self, obj):
        customs_numbers = [
            product.id
            for product in obj.batch_items.all()
            if getattr(product.order, 'order_type', -1) == OrderType.CUSTOM_ORDER
        ]
        if len(customs_numbers) == 0:
            return ''
        else:
            return '''
                <span style='color:#C74909;font-size: 20px;'>
                Custom ids: {}</span>
            '''.format(
                ', '.join(str(x) for x in customs_numbers)
            )

    check_for_customs.short_description = 'Custom in batch?'


class ProductBatchComplaintAdmin(ProductBatchAdmin):
    list_display = (
        'id',
        'elements_order',
        'elements_order_manufactor_fault',
        'manufactor',
        'actions_needed',
        'admin_get_reproduction_time_in_days',
        'product_type',
        'is_meblepl_upload_confirmed',
        'check_for_production_files',
        'check_for_labels',
        'get_batch_items_number_and_link_complaint',
        'get_batch_type',
        'created_at',
        'batch_value',
    )
    inlines = (
        ProductBatchDetailsJettyInline,
        ProductBatchDetailsWattyInline,
    )
    change_form_template = 'admin/producers/batch/change_form.html'
    search_fields = ('id',)
    list_filter = (
        ('created_at', DateRangeFilter),
        'status',
        'manufactor',
        'actions_needed',
        'batch_type',
        ReproductionTimeFilterProductBatch,
        ShelfTypeFilterProductBatch,
        ProductBatchComplaintManufactorFaultFilter,
        MultipleColoursProductBatchComplaintFilter,
    )
    actions = (
        admin_actions.sum_usage_for_all_items_from_batch,
        admin_actions.sum_usage_for_all_items_from_batch_async,
        admin_actions.sum_usage_for_all_items_from_batch_telmex_async,
        admin_actions.export_for_production_timeline,
        admin_actions.export_for_production_timeline_with_labels,
        admin_actions.recalculate_all_batch_files,
        admin_actions.recalculate_batch_nesting_files,
        admin_actions.check_for_all_files_manually,
        admin_actions.generate_meblepl_package,
        admin_actions.get_cardboard_merged_file,
        admin_actions.get_order_summary_xls,
        admin_actions.get_order_summary_xls_man_fault,
        admin_actions.get_usage_in_xml,
        admin_actions.get_usage_in_xml_with_match,
        admin_actions.create_production_order,
        admin_actions.get_speedtrans_order_type01_doors,
        admin_actions.get_speedtrans_order_type01_drawers,
        admin_actions.upload_batches_to_meblepl,
        admin_actions.get_nameplate_files,
        'send_email_to_manufactor_with_order_files',
        admin_actions.generate_t25_legs_report_batches,
        admin_actions.create_carton_files_for_s93,
    )

    @admin.display(description='Elem Order Man Fault')
    def elements_order_manufactor_fault(self, obj):
        if obj.elementsorders:
            elements_order = [
                str(x.id) for x in obj.elementsorders if x.is_manufactor_fault
            ]
            return ', '.join(elements_order) if elements_order else None

    def send_email_to_manufactor_with_order_files(self, request, queryset):
        batches_group_by_manufactor = defaultdict(list)
        for batch in queryset:
            batches_group_by_manufactor[batch.manufactor.id].append(batch.id)
        for manufactor_id, batch_ids in batches_group_by_manufactor.items():
            send_auto_email_with_files_to_manufactor.delay(manufactor_id, batch_ids)

    def get_queryset(self, request):
        return super().get_queryset(request, exclude_complaint=False)

    def get_batch_items_number_and_link_complaint(self, obj):
        return self.get_batch_items_number_and_link(obj, complaint=True)

    get_batch_items_number_and_link_complaint.short_description = 'Items'


class ManufactorAdditionalAccountsAdmin(admin.ModelAdmin):
    raw_id_fields = ('user',)
    list_display = ('id', '__str__', 'access_level', 'manufactor')
    list_filter = ('access_level', 'manufactor')


class ProductDetailsJettyAdmin(ProductDetailsFilesChangelogMixin, admin.ModelAdmin):
    """Bare admin with links to production files. Added because Product is too slow."""

    fieldsets = (
        (
            'Files',
            {
                'fields': (
                    'instruction',
                    'front_view',
                    'front_view_zip',
                    'packaging_instruction',
                    'cnc_connections',
                    'cnc_connections_zip',
                    'production_drawings',
                    'vertical_labels',
                )
            },
        ),
    ) + ProductDetailsFilesChangelogMixin.fieldsets
    readonly_fields = ProductDetailsFilesChangelogMixin.readonly_fields

    change_form_template = 'admin/producers/batch/change_form.html'


class ProductDetailsWattyAdmin(ProductDetailsFilesChangelogMixin, admin.ModelAdmin):
    """Bare admin with links to production files. Added because Product is too slow."""

    fieldsets = (
        (
            'Files',
            {
                'fields': (
                    'instruction',
                    'front_view',
                    'front_view_zip',
                    'packaging_instruction',
                    'cnc_connections_zip',
                    'production_drawings',
                )
            },
        ),
    ) + ProductDetailsFilesChangelogMixin.fieldsets
    readonly_fields = ProductDetailsFilesChangelogMixin.readonly_fields

    change_form_template = 'admin/producers/batch/change_form.html'


class ProductDetailsSottyAdmin(ProductDetailsFilesChangelogMixin, admin.ModelAdmin):
    """Bare admin with links to production files."""

    fieldsets = (
        (
            'Files',
            {
                'fields': (
                    'front_view',
                    'labels_packaging',
                    'instruction',
                )
            },
        ),
    ) + ProductDetailsFilesChangelogMixin.fieldsets
    readonly_fields = ProductDetailsFilesChangelogMixin.readonly_fields

    change_form_template = 'admin/producers/batch/change_form.html'


class ManufacturerReleasedPackageAdmin(admin.ModelAdmin):
    raw_id_fields = ('product',)
    list_display = (
        'id',
        'product',
        'package',
        'released_at',
        'manufacturer',
    )
    list_filter = ('released_at',)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related('product')


class ProductComplaintAdmin(ProductAdmin, ProductAdminQuerySetMixin):
    list_display = (
        'id',
        'manufactor',
        'get_elements_order',
        'batch_link',
        'reproduction_time_in_days',
        'express_replacement',
        'manufactor_fault',
        'priority',
        'get_item_color',
        'get_order_id',
        'get_order_customer_as_string',
        'get_order_country_as_string',
        'status',
        'shelf_type',
        'get_serialization_status',
        'created_at',
        'updated_at',
        'estimated_order_production_date',
        'get_cached_product_type',
        'check_for_production_files',
        'get_pdf_for_packaging_complaints',
    )
    list_display_links = (
        'id',
        'manufactor',
    )

    search_fields = (
        'id',
        'order__id',
    )

    readonly_fields = ProductAdmin.readonly_fields + ('manufactor',)

    raw_id_fields = (
        'additional_order_items',
        'order',
        'order_item',
        'copy_of',
    )
    list_filter = (
        ('created_at', DateRangeFilter),
        ProductStatusFilter,
        ExpressReplacementFilter,
        ProductComplaintManufactorFaultFilter,
        ShelfTypeFilterProduct,
        'manufactor',
        MultipleChoiceListCustomProductInProductionFilter,
        'is_desk',
        ('order__estimated_delivery_time', DateRangeFilter),
        'cached_configurator_type',
        'cached_physical_product_version',
        MultipleChoicePriorityFilter,
        ToBeShippedFilter,
        'reproduction_time_in_days',
        IsBatchedFilter,
        MultipleChoiceProductComplaintReproductionElementsFilter,
        DnaStyleFilter,
    )
    actions = (
        admin_actions.admin_change_priority_for_products,
        admin_actions.batch_with_manufactor,
        admin_actions.change_status_with_history,
        admin_actions.admin_change_manufactor_for_products,
        admin_actions.export_files_as_zip,
        admin_actions.front_views_package_recalculate,
        admin_actions.packaging_instruction_package_recalculate,
        admin_actions.front_connection_and_packaging_recalculate,
        admin_actions.recalc_serialization_using_ps,
        admin_actions.create_connections,
        admin_actions.ivy_product_usage,
        admin_actions.get_filtered_batching_tool_complaints,
        admin_actions.export_for_production_timeline_product,
        admin_actions.export_for_production_timeline_with_labels,
        admin_actions.get_elements_csv_meble_pl,
        admin_actions.get_cardboard_merged_file_meblepl,
        admin_actions.get_cardboard_merged_file_drewtur,
        admin_actions.fetch_production_drawings_from_ps,
        admin_actions.export_selected_products_for_file_complaints,
        admin_actions.get_order_summary_xls_for_products,
        admin_actions.get_order_summary_xls_for_products_by_batches,
        admin_actions.remove_products_from_batch,
        'run_auto_batching_for_selected_products',
        admin_actions.generate_t25_legs_report_products,
        admin_actions.gala_batching_tool,
    )
    button_actions = []

    def get_queryset(self, request):
        queryset = super().get_queryset(request, exclude_complaint=False)
        return queryset.select_related('copy_of').prefetch_related('order__complaints')

    def run_auto_batching_for_selected_products(self, request, queryset):
        create_complaint_batches.delay(list(queryset.values_list('id', flat=True)))

    def get_pdf_for_packaging_complaints(self, object):
        return get_pdf_for_packaging(object, complaints=True)

    @admin.display(boolean=True, description='ExpRep')
    def express_replacement(self, obj):
        return obj.order.is_to_be_shipped_complaint_express_replacement()

    @admin.display(boolean=True, description='Man Fault')
    def manufactor_fault(self, obj):
        complaints = list(obj.reproduction_complaints.all())
        return complaints[0].manufactor_fault if complaints else False

    def has_delete_permission(self, request, obj=None):
        if not obj:
            return False
        is_express_replacement = None
        is_batch = obj.batch is not None
        if obj.reproduction_complaints.exists():
            is_express_replacement = (
                obj.reproduction_complaints.first().express_replacement
            )
        return not any([is_express_replacement, is_batch])

    def delete_model(self, request, obj):
        if not obj:
            return super().delete_model(request, obj)

        if obj.reproduction_complaints.exists():
            obj.reproduction_complaints.first().delete()
        if obj.order.logistic_info:
            obj.order.change_status(OrderStatus.CANCELLED)
        return super().delete_model(request, obj)


class ProductQualityAdmin(ProductAdminGetItemColorMixin, admin.ModelAdmin):
    raw_id_fields = (
        'copy_of',
        'order',
        'order_item',
        'batch',
    )
    save_on_top = True
    list_display_links = (
        'id',
        'manufactor',
    )
    list_max_show_all = 800
    list_per_page = 50
    readonly_fields = (
        'status',
        'previous_status',
        'delivered_at',
    )
    search_fields = (
        'id',
        'order__id',
        'reproduction_products__id',
    )
    list_editable = ('quality_notes',)
    actions = (
        admin_actions.change_status_with_history,
        admin_actions.ivy_product_usage,
        admin_actions.export_products_for_production_timeline_with_labels_quality,
        admin_actions.change_quality_control_needed,
        admin_actions.change_status_to_quality_blocker,
    )
    fieldsets = (
        (
            'Product',
            {
                'fields': (
                    'manufactor',
                    'status',
                    'priority',
                    'quality_control_needed',
                ),
            },
        ),
        (
            'Quality',
            {
                'fields': (
                    'quality_priority',
                    'quality_notes',
                    'quality_date',
                    'quality_result',
                    'quality_report_link',
                ),
            },
        ),
    )

    list_display = (
        'id',
        'order_id',
        'manufactor',
        'get_item_color',
        'get_customer',
        'get_country',
        'status',
        'priority',
        'quality_control_needed',
        'shelf_type',
        'estimated_order_production_date',
        'show_front_view',
        'get_number_of_packages',
        # Logistic Order
        'get_carrier',
        # AS Planning
        'is_complaint_assembly_service',
        # Production Complaint
        'get_complaint_number',
        # Complaints In Production
        'get_reproduction_products',
        # Free Return
        'is_free_return',
        # Reviews
        'get_reviews',
        # Quality
        'quality_priority',
        'quality_notes',
        'quality_date',
        'quality_result',
        'quality_report_link',
    )
    list_filter = (
        ('order__estimated_delivery_time', DateRangeFilter),
        ProductBatchingFilter,
        ProductStatusFilter,
        'cached_configurator_type',
        'cached_physical_product_version',
        'order__assembly',
        MultipleChoicePriorityFilter,
        'quality_control_needed',
        'order__returning_client',
        ToBeShippedFilter,
        AcceptedToProductionFilter,
        ShelfTypeFilterProduct,
        MultipleChoiceListCustomProductInProductionFilter,
        'manufactor',
        ProductDescriptionFilter,
        ProductQualityComplaintAssemblyServiceFilter,
        ProductQualityHasComplaintFilter,
        ProductQualityHasFreeReturnFilter,
        ProductQualityReviewScoreFilter,
    )

    def has_delete_permission(self, request, obj=None):
        return False

    @admin.display(description='Customer')
    def get_customer(self, obj):
        return obj.order.full_name

    @admin.display(description='Country')
    def get_country(self, obj):
        return obj.order.country

    @mark_safe
    @admin.display(description='Shelf type')
    def shelf_type(self, obj):
        return obj.get_shelf_type(with_configurator_type=True)

    @admin.display(description='Order Estimated Production Date')
    def estimated_order_production_date(self, obj):
        return obj.order.estimated_delivery_time

    @mark_safe
    @admin.display(description='Tools for customization')
    def show_front_view(self, obj, complaints=False):
        result = ''
        if obj.product_type in Product.PS_SERIALIZED:
            front_view_link = get_display_front_view_link(obj)
            result += f'<br/><a href="{front_view_link}">Display front view</a><br/>'
        return result

    @admin.display(description='Numbers of packages')
    def get_number_of_packages(self, obj):
        return obj.get_packaging_quantity()

    @staticmethod
    @admin.display(description='Carrier')
    def get_carrier(product):
        logistic_order = product.get_logistic_order()
        return logistic_order.carrier if logistic_order else 'No LogisticOrder related'

    @staticmethod
    @admin.display(description='Complaint Assembly Service', boolean=True)
    def is_complaint_assembly_service(product):
        try:
            return product.latest_complaint[0].assembly_team_intervention
        except (AttributeError, IndexError):
            return False

    @admin.display(description='Numbers of complaints')
    def get_complaint_number(self, product):
        return product.complaint_count

    @admin.display(description='Reproduction products')
    def get_reproduction_products(self, product):
        reproduction_products_ids = [p.id for p in product.latest_reproduction_products]
        return (
            reproduction_products_ids
            if reproduction_products_ids
            else 'No repr. products'
        )

    @admin.display(description='Has free return?', boolean=True)
    def is_free_return(self, product):
        try:
            return product.order_item.free_return is not None
        except AttributeError:
            return False

    @admin.display(description='Review score')
    def get_reviews(self, product):
        try:
            return product.order.latest_review[0].score
        except (AttributeError, IndexError):
            return 'No review'


class AutoBatchingMailingConfigurationAdmin(admin.ModelAdmin):
    form = AutoBatchingMailingConfigurationForm
    list_display = ('id', 'manufactor', 'get_shelf_type', 'feature')
    list_filter = ('manufactor',)
    fields = (
        'manufactor',
        ('shelf_type', 'include_all_shelf_types'),
        'feature',
        'email',
        'cc_email',
        'mail_subject',
        'mail_body',
        'attach_order_xlsx',
        'attach_match_usage',
        'attach_summary_usage',
        'attach_summary_usage_for_telmex',
        'attach_t13_handles',
        'attach_production_timeline_with_labels',
        'attach_t1_drawer_handle',
        'attach_t1_door_handle',
        'attach_nameplate_zip',
        'attach_t25_legs_report',
        'attach_nesting_desk_beam',
        'attach_s93_karton',
    )

    @admin.display(description='Shelf type')
    def get_shelf_type(self, obj: AutoBatchingMailingConfiguration) -> str:
        return (
            ShelfType(obj.shelf_type).name
            if not obj.include_all_shelf_types
            else constants.ALL_SHELF_TYPES_STR
        )


class BatchMailingStatusAdmin(admin.ModelAdmin):
    list_display = ('id', 'mail_conf', 'get_batches', 'status', 'created_at')
    readonly_fields = ('mail_conf', 'batches', 'status', 'created_at')
    list_filter = ('status',)
    search_fields = ('id', 'mail_conf__id', 'batches__id')
    raw_id_fields = ('batches',)

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related('batches')

    @admin.display(description='Batches')
    def get_batches(self, obj):
        return ', '.join(str(batch.id) for batch in obj.batches.all())


class BatchingSettingsAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'manufactor',
        'shelf_type',
        'separate_desks',
        'separate_s_plus',
        'min_size',
        'max_size',
        'max_area',
    )


class QualityHoldReleaseRequestAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'batch',
        'status',
        'created_at',
        'accepted_at',
        'rejected_at',
        'get_products_with_statuses',
    )
    list_filter = (IsRequestAcceptedFilter, IsRequestRejectedFilter)
    readonly_fields = (
        'created_at',
        'accepted_at',
        'accepted_by',
        'rejected_at',
        'rejected_by',
    )
    actions = ['accept_requests', 'reject_requests']

    @admin.display(description='Products with Statuses')
    def get_products_with_statuses(self, obj):
        product_status_list = [
            f'{product.id}: {product.get_status_display()}'
            for product in obj.products.all()
        ]
        return format_html('<br>'.join(product_status_list))

    def _process_requests(self, request, queryset, action_func, action_name):
        success_count = error_count = 0
        for release_request in queryset:
            try:
                action_func(release_request, request.user)
                success_count += 1

            except ValueError as e:
                self.message_user(
                    request,
                    f'Error processing request {release_request.id}: {str(e)}',
                    level=messages.ERROR,
                )
                error_count += 1

        if success_count:
            self.message_user(
                request,
                f'{action_name.capitalize()} successful for {success_count} request(s)',
                level=messages.SUCCESS,
            )
        if error_count:
            self.message_user(
                request,
                f'{action_name.capitalize()} failed for {error_count} request(s)',
                level=messages.WARNING,
            )

    @admin.action(description='Accept selected quality hold release requests')
    def accept_requests(self, request, queryset):
        self._process_requests(
            request,
            queryset,
            lambda request_obj, user: request_obj.accept(user),
            'accept',
        )

    @admin.action(description='Reject selected quality hold release requests')
    def reject_requests(self, request, queryset):
        self._process_requests(
            request,
            queryset,
            lambda request_obj, user: request_obj.reject(user),
            'reject',
        )


admin.site.register(Product, ProductAdmin)
admin.site.register(ProductAborted, ProductAdmin)
admin.site.register(Manufactor, ManufactorAdmin)
admin.site.register(ProductBatch, ProductBatchAdmin)
admin.site.register(ProductBatchComplaint, ProductBatchComplaintAdmin)
admin.site.register(ManufactorAdditionalAccounts, ManufactorAdditionalAccountsAdmin)
admin.site.register(ProductDetailsJetty, ProductDetailsJettyAdmin)
admin.site.register(ProductDetailsWatty, ProductDetailsWattyAdmin)
admin.site.register(ProductDetailsSotty, ProductDetailsSottyAdmin)
admin.site.register(ManufacturerReleasedPackage, ManufacturerReleasedPackageAdmin)
admin.site.register(ProductComplaint, ProductComplaintAdmin)
admin.site.register(ProductQuality, ProductQualityAdmin)
admin.site.register(
    AutoBatchingMailingConfiguration, AutoBatchingMailingConfigurationAdmin
)
admin.site.register(BatchMailingStatus, BatchMailingStatusAdmin)
admin.site.register(BatchingSettings, BatchingSettingsAdmin)
admin.site.register(QualityHoldReleaseRequest, QualityHoldReleaseRequestAdmin)
