import datetime

from datetime import (
    date,
    timedelta,
)

from django.conf import settings
from django.contrib.auth.models import User
from django.utils import timezone

from custom.utils.emails import send_html_mail
from producers.choices import (
    DeliveryPriority,
    ProductPriority,
    ProductStatus,
    SourcePriority,
)
from producers.errors import CannotChangeToPostponePriorityException
from producers.internal_api.events import ProductPriorityChangedEvent
from producers.product_updater import (
    _add_log,
    get_owner_or_default_user,
)


class ProductPriorityUpdater:
    DELIVERY_DAYS_NORMAL = 7
    DELIVERY_DAYS_WITH_ASSEMBLY = 14
    PRODUCTION_IN_WEEKS = 4
    MINIMAL_POSTPONED_IN_WEEKS = 2

    def __init__(self, product: 'producers.Product'):  # noqa: F821
        self.product = product
        self.order = product.order

    def _add_product_priority_history(
        self, priority_to_change, owner, previous_priority
    ):
        from producers.models import ProductPriorityHistory

        return ProductPriorityHistory.objects.create(
            product=self.product,
            owner=get_owner_or_default_user(owner),
            priority=priority_to_change,
            previous_priority=previous_priority,
        )

    def change_priority(self, priority_to_change, previous_priority=None, owner=None):
        previous_priority = previous_priority or self.product.priority
        self.product.priority = priority_to_change
        if previous_priority in ProductPriority.on_hold_priorities():
            self.update_estimated_delivery_time_after_onhold()
        self.product.save(update_fields=['priority'])
        ProductPriorityChangedEvent(self.product)
        self._add_product_priority_history(priority_to_change, owner, previous_priority)
        _add_log(
            self.product,
            owner,
            'change_priority',
            data={
                'new': ProductPriority(priority_to_change).label,
                'previous': (
                    ProductPriority(previous_priority).label
                    if previous_priority
                    else ''
                ),
            },
        )

    def change_to_postponed(self, requested_postponed_delivery_date):
        order = self.product.order
        has_assembly = any(
            logistic_order.assembly_type for logistic_order in order.logistic_info
        )
        delivery_offset = (
            self.DELIVERY_DAYS_NORMAL
            if not has_assembly
            else self.DELIVERY_DAYS_WITH_ASSEMBLY
        )
        postponed_production_date = requested_postponed_delivery_date - timedelta(
            days=delivery_offset
        )
        expected_postponed_release_date = postponed_production_date - timedelta(
            weeks=self.PRODUCTION_IN_WEEKS
        )
        if expected_postponed_release_date <= date.today() + timedelta(
            weeks=self.MINIMAL_POSTPONED_IN_WEEKS
        ):
            raise CannotChangeToPostponePriorityException(
                expected_postponed_release_date
            )

        self.product.requested_postponed_delivery_date = (
            requested_postponed_delivery_date
        )
        self.product.expected_postponed_release_date = expected_postponed_release_date
        self.product.save(
            update_fields=[
                'requested_postponed_delivery_date',
                'expected_postponed_release_date',
            ]
        )
        self.change_priority(ProductPriority.POSTPONED)

        order.change_estimated_delivery_time(postponed_production_date)
        return self.product

    def update_estimated_delivery_time_after_onhold(self):
        previous_priority_change_log = self.product.product_priority_history.first()
        if not (
            previous_priority_change_log
            and previous_priority_change_log.priority
            in ProductPriority.on_hold_priorities()
        ):
            return

        on_hold_status_duration_days = self.get_duration_of_status(
            previous_priority_change_log
        )
        if on_hold_status_duration_days >= settings.ON_HOLD_PRIORITY_DAYS_THRESHOLD:
            days_exceeded = (
                on_hold_status_duration_days - settings.ON_HOLD_PRIORITY_DAYS_THRESHOLD
            )
            order = self.product.order
            order.change_estimated_delivery_time(
                order.estimated_delivery_time
                + datetime.timedelta(days=days_exceeded + 1)
            )

    @staticmethod
    def get_duration_of_status(last_priority_history):
        if last_priority_history:
            return (timezone.now() - last_priority_history.created_at).days

    def set_source_priority_on_init(self, user: User | None = None):
        if self.order.is_vip_order:
            self.change_source_priority(SourcePriority.VIP, user)
        elif self.order.is_influ_order:
            self.change_source_priority(SourcePriority.INFLU, user)
        elif self.order.is_big_order:
            self.change_source_priority(SourcePriority.BIG_ORDER, user)

    def change_source_priority(
        self,
        new_priority: SourcePriority,
        user: User | None = None,
        previous_priority: SourcePriority | None = None,
    ):
        previous_priority = previous_priority or SourcePriority(
            self.product.source_priority
        )
        self.product.source_priority = new_priority.value
        self.product.save(update_fields=['source_priority'])
        _add_log(
            self.product,
            user,
            'change_source_priority',
            data={
                'new': new_priority.label,
                'previous': previous_priority.label,
            },
        )

    def change_delivery_priority(
        self,
        new_priority: DeliveryPriority,
        user: User | None = None,
        previous_priority: DeliveryPriority | None = None,
    ):
        previous_priority = previous_priority or DeliveryPriority(
            self.product.delivery_priority
        )
        self.product.delivery_priority = new_priority.value
        self.product.save(update_fields=['delivery_priority'])
        _add_log(
            self.product,
            user,
            'change_delivery_priority',
            data={
                'new': new_priority.label,
                'previous': previous_priority.label,
            },
        )

    def set_assembly_delivery_priority(self):
        self.change_delivery_priority(DeliveryPriority.ASSEMBLY)

    def set_normal_delivery_priority(self):
        self.change_delivery_priority(DeliveryPriority.NORMAL)

    def set_not_delivered_delivery_priority(self) -> None:
        self.change_delivery_priority(DeliveryPriority.NOT_DELIVERED)
        self.product.status_updater.change_status(ProductStatus.QUALITY_BLOCKER)
        email_data = {
            'product_id': self.product.id,
            'batch_id': self.product.batch_id if self.product.batch else None,
        }
        manufacturer = self.product.manufactor
        if not manufacturer:
            return
        manufacturer_recipients = settings.MANUFACTURERS_RECIPIENTS.get(manufacturer.id)
        for _, email_address in manufacturer_recipients:
            send_html_mail(
                template_name='mail_notify_manufacturer_quality_blocker.html',
                subject='Zgłoszenie zwrotu mebla do fabryki '
                '– prośba o sprawdzenie i przepakowanie',
                context=email_data,
                to=email_address,
            )
