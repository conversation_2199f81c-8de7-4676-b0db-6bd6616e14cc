from copy import deepcopy
from itertools import chain
from typing import Iterable

from producers.gh.moduly_glowne.ivy_elements import Pack
from producers.models import Product


class CoverPackagingService:
    MAX_PACKAGES_IN_BOX = 4
    MIN_PACKAGES_IN_BOX = 2
    BOX_DIM_X = 550
    BOX_DIM_Y = 430
    BOX_DIM_Z = 200

    def __init__(self, products: Iterable[Product]):
        self.products = products

    def get_packing(self) -> list[Pack]:
        unwrap_packages = self.get_unwrap_packages()
        wrap_packages = []
        pack_id = 1
        while len(unwrap_packages) >= self.MIN_PACKAGES_IN_BOX:
            packages_in_box = self.MAX_PACKAGES_IN_BOX
            if (
                0
                < len(unwrap_packages) % self.MAX_PACKAGES_IN_BOX
                < self.MIN_PACKAGES_IN_BOX
            ):
                # if there are 5 covers, we need to pack them in two boxes 2 + 3
                packages_in_box = self.MIN_PACKAGES_IN_BOX
            pack = self.pack_to_box(unwrap_packages[:packages_in_box])
            pack.pack_id = pack_id
            pack_id += 1
            wrap_packages.append(pack)
            unwrap_packages = unwrap_packages[packages_in_box:]
        if unwrap_packages:
            pack = self.pack_to_foliage(unwrap_packages[0])
            pack.pack_id = pack_id
            wrap_packages.append(pack)

        return wrap_packages

    def get_unwrap_packages(self) -> list[Pack]:
        return list(
            chain.from_iterable(
                [product.get_jetty_serialized().packaging for product in self.products]
            )
        )

    def pack_to_box(self, packages: list[Pack]) -> Pack:
        box = deepcopy(packages[0])
        box.is_group_pack = True
        box.dim_x = self.BOX_DIM_X
        box.dim_y = self.BOX_DIM_Y
        box.dim_z = self.BOX_DIM_Z
        box.weight = 0
        box.adjusted_weight = 0
        for package in packages:
            box.product_ids.append(package.product.id)
            box.weight += package.weight
            box.adjusted_weight += package.adjusted_weight
            box.all_elements.extend(package.all_elements)
        return box

    @classmethod
    def pack_to_foliage(cls, package: Pack) -> Pack:
        package.product_ids.append(package.product.id)
        package.is_group_pack = True
        return package
