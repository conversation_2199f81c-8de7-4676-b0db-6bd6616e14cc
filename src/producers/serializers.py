import logging

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

from rest_framework import serializers

from producers.models import (
    Manufactor,
    ManufacturerReleasedPackage,
    Product,
)
from producers.models_split.product_batch_details import (
    ProductBatchDetailsJetty,
    ProductBatchDetailsSotty,
    ProductBatchDetailsWatty,
)
from producers.models_split.product_details import (
    ProductDetailsJetty,
    ProductDetailsSotty,
    ProductDetailsWatty,
)

User = get_user_model()

logger = logging.getLogger('producers')


class ProductDetailsJettySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductDetailsJetty
        fields = (
            'id',
            'instruction',
            'cnc_connections',
            'cnc_connections_zip',
            'front_view',
            'horizontals_pdf',
            'vertical_labels',
            'nesting_data_for_batch',
            'packaging_instruction',
            'package_labels',
            'cached_serialization',
        )


class ProductDetailsWattySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductDetailsWatty
        fields = ('cached_serialization',)


class ProductDetailsSottySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductDetailsSotty
        fields = ('cached_serialization',)


class ProductBatchDetailsJettySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductBatchDetailsJetty
        fields = (
            'id',
            'nesting',
            'nesting_backs',
            'nesting_drawers',
            'nesting_bottom_drawers',
            'nesting_front_drawers',
            'nesting_handle_blende',
            'nesting_desk_beam',
            'labels_packaging',
            'labels_logistic',
            'labels_verticals',
            'packaging_csvs',
            'nesting_zip',
            'meblepl_zip',
            'rotated_elements',
        )


class ProductBatchDetailsWattySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductBatchDetailsWatty
        fields = (
            'id',
            'nesting_zip',
            'nesting_bar',
            'nesting_mask',
            'nesting_drawer_synchro',
            'nesting_hang_slat',
            'nesting_led_profile',
            'lighting_completion_list',
            'labels_elements',
            'labels_packaging',
            'labels_adapters',
            'labels_logistic',
            'packaging_csvs',
            'rotated_elements',
        )


class ProductBatchDetailsSottySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductBatchDetailsSotty
        fields = (
            'labels_elements',
            'labels_packaging',
            'cached_serialization',
        )


# TODO: change to modelSeralizer,
#  add more logic around proper packages names and consistent
class ManufactorReleasedPackageSerializer(serializers.Serializer):
    # Unfortunately PolishCamelCase used, but drf has no
    # proper ways to deal with this scenario easy way,
    # ref. https://github.com/encode/django-rest-framework/issues/967
    Regal = serializers.CharField(source='product')
    Karton = serializers.CharField(source='package')
    DataCzas = serializers.CharField(source='released_at')

    def validate_Regal(self, value):
        try:
            _, product_id = value.split('-')
        except ValueError as exc:
            logger.exception(
                'Release exception. Regal %s has wrong format. Exc: %s', value, exc
            )
            raise ValidationError(
                f'Regal: {value} has wrong format. '
                f'Proper format is <product_type>-<product_id>'
            )

        if not Product.objects.filter(id=product_id).exists():
            logger.warning(f'Could not find Product with id: {product_id}')
            raise ValidationError(f'Could not find Product with id: {product_id}')

        return product_id

    def validate_Karton(self, value):
        try:
            (
                product_type,
                product_id,
                package_number,
                all_packages_quantity,
            ) = value.split('-')
            package_number = int(package_number)
            all_packages_quantity = int(all_packages_quantity)
        except ValueError as exc:
            logger.exception(
                'Regal exception %s. Karton %s has wrong format ', exc, value
            )
            raise ValidationError(
                f'Karton: {value} has wrong format. '
                f'Proper format is '
                f'<product_type>-<product_id>-<package_number>-<product_total_package_number>'  # noqa: E501
            )
        try:
            product = Product.objects.get(id=product_id)
        except Product.DoesNotExist:
            logger.warning('Could not find Product with id %s', product_id)
            raise ValidationError(f'Could not find Product with id: {product_id}')

        if all_packages_quantity != product.get_packaging_quantity():
            logger.warning(
                'Package quantity does not match quantity from product id: '
                '%s get: %d  should be: %d',
                product_id,
                all_packages_quantity,
                product.get_packaging_quantity(),
            )
            raise ValidationError(
                f'Packages quantity does not match quantity '
                f'from Product id: {product_id}'
            )

        if package_number < 1 or package_number > all_packages_quantity:
            logger.warning(
                'Number of package does not match quantity of packages. Karton: %s',
                value,
            )
            raise ValidationError(
                f'Number of package does not match quantity of packages. '
                f'Karton: {value}'
            )

        return package_number

    def create(self, validated_data, *args, **kwargs):
        user = self.context['request'].user
        # it will never raise DoesNotExists because we validate it in permission
        manufacturer = Manufactor.objects.get(owner=user)
        product_id = validated_data['product']
        package_number = validated_data['package']
        released_package, created = ManufacturerReleasedPackage.objects.get_or_create(
            product_id=product_id,
            manufacturer=manufacturer,
            package=package_number,
            defaults={
                'released_at': validated_data['released_at'],
            },
        )

        product_status_updater = released_package.product.status_updater
        if product_status_updater.can_change_status_to_be_shipped():
            product_status_updater.change_status_to_be_shipped_after_release()

        return released_package


class ProductStatusSerializer(serializers.ModelSerializer):

    accepted_packages = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = ['id', 'status', 'accepted_packages']

    @staticmethod
    def get_accepted_packages(obj):
        return [p.package for p in obj.manufacturerreleasedpackage_set.all()]
