# Generated by Django 4.2.23 on 2025-07-11 15:29

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0002_email_max_length'),
    ]

    operations = [
        migrations.RunSQL(
            sql='''ALTER TABLE account_emailaddress DROP CONSTRAINT IF EXISTS account_emailaddress_email_key;''',
            reverse_sql='''ALTER TABLE account_emailaddress ADD CONSTRAINT account_emailaddress_email_key UNIQUE (email);''',
        ),
    ]
