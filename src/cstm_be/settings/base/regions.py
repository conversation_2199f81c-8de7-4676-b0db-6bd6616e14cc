from typing import Final

# Regions with assembly service available
ASSEMBLY_REGION_KEYS: Final[frozenset[str]] = frozenset(
    {
        'austria',
        'belgium',
        'denmark',
        'france',
        'germany',
        'luxembourg',
        'netherlands',
        'poland',
        'switzerland',
        'united_kingdom',
    }
)
# Regions with corduroy restrictions
CORDUROY_RESTRICTED_REGIONS: Final[frozenset[str]] = frozenset({'united_kingdom'})
# Regions with `Old Sofa Collection` service available
OLD_SOFA_COLLECTION_REGIONS: Final[frozenset[str]] = frozenset(
    {
        'austria',
        'belgium',
        'france',
        'germany',
        'luxembourg',
        'netherlands',
    }
)
# Regions with S01 (sofa) availability
# remember to set distance type in `SOTTY_DELIVERY_REGIONS_MAP`
S01_REGION_KEYS: Final[frozenset[str]] = frozenset(
    {
        'austria',
        'belgium',
        'czech',
        'denmark',
        'finland',
        'france',
        'germany',
        'ireland',
        'italy',
        'luxembourg',
        'netherlands',
        'norway',
        'poland',
        'portugal',
        'slovakia',
        'spain',
        'sweden',
        'switzerland',
        'united_kingdom',
    }
)
# Regions with T03 availability
T03_REGION_KEYS = ASSEMBLY_REGION_KEYS
# Regions with `White Gloves Delivery` service available
WHITE_GLOVES_DELIVERY_REGION_KEYS: Final[frozenset[str]] = frozenset(
    {
        'austria',
        'belgium',
        'czech',
        'denmark',
        'france',
        'germany',
        'italy',
        'luxembourg',
        'netherlands',
        'poland',
        'portugal',
        'slovakia',
        'spain',
        'switzerland',
        'united_kingdom',
    }
)
