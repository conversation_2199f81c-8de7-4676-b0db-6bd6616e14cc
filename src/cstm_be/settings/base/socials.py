from .common import env

# disable redirect to backend login template
SOCIALACCOUNT_LOGIN_ON_GET = True
# adapters
ACCOUNT_ADAPTER = 'socials.social_login_adapter.AccountAdapter'
SOCIALACCOUNT_ADAPTER = 'socials.social_login_adapter.SocialAccountAdapter'
# to be consistent with our standard authorization flow we need to use username
ACCOUNT_AUTHENTICATION_METHOD = 'username'
# we use username as unique identifier
ACCOUNT_UNIQUE_EMAIL = False
# scope definition for linking social accounts to already existing users
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'APP': {
            'client_id': env.str('GOOGLE_CLIENT_ID', default=''),
            'secret': env.str('GOOGLE_SECRET', default=''),
        },
        'SCOPE': [
            'profile',
            'email',
        ],
    },
    'facebook': {
        'APP': {
            'client_id': env.str('FACEBOOK_CLIENT_ID', default=''),
            'secret': env.str('FACEBOOK_SECRET', default=''),
        },
        'SCOPE': [
            'email',
            'public_profile',
        ],
    },
    'apple': {
        'APP': {
            'client_id': env.str('APPLE_CLIENT_ID', default=''),
            'secret': env.str('APPLE_SECRET', default=''),
            'key': env.str('APPLE_KEY_ID', default=''),  # this is team id
            'certificate_key': f"-----BEGIN PRIVATE KEY-----\n{env.str('APPLE_CERTIFICATE_KEY', default='')}\n-----END PRIVATE KEY-----",  # noqa
        },
        'SCOPE': [
            'email',
        ],
    },
}
