from abc import abstractmethod
from decimal import Decimal
from typing import (
    TYPE_CHECKING,
    Union,
)

from django.db import models
from django.db.models import (
    QuerySet,
    Sum,
    Value,
)
from django.db.models.functions import Coalesce
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from custom.enums import ShelfType
from custom.models import Countries
from custom.utils.strings import format_price
from gallery.enums import SellableItemContentTypes
from orders.choices import VatType
from orders.enums import VatChoices
from orders.services.vat_details import VatDetailsGetter
from regions.services.limitations import LimitationService

if TYPE_CHECKING:
    from carts.models import (
        Cart,
        CartItem,
    )
    from orders.models import (
        Order,
        OrderItem,
    )


class PriceAbstractModel(models.Model):
    """An abstract model for price-related fields and methods.

    Used by Order and Cart models.
    """

    total_price = models.DecimalField(  # how much company earns in EUR on this Order
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
    )
    total_price_net = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
    )
    region_total_price = models.DecimalField(  # how much customer pays in currency
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
    )
    region_total_price_net = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
    )
    promo_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        blank=True,
    )
    promo_amount_net = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        blank=True,
    )
    region_promo_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        blank=True,
    )
    region_promo_amount_net = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        blank=True,
    )

    # fields that price depends on
    assembly = models.BooleanField(default=False)
    region_vat = models.BooleanField(default=False)
    vat_type = models.PositiveSmallIntegerField(
        choices=VatChoices.choices,
        default=VatChoices.VAT_NORMAL,
    )
    vat_rate = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        null=True,
        blank=True,
    )

    # relations that price depends on
    region = models.ForeignKey('regions.Region', on_delete=models.PROTECT)
    used_promo = models.ForeignKey(
        'vouchers.Voucher',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )

    class Meta:
        abstract = True

    @property
    def promo_text(self) -> str:
        return self.used_promo.code if self.used_promo else ''

    @property
    @abstractmethod
    def is_order(self) -> bool:
        pass

    @property
    def common_fields(self) -> dict:
        """Fields shared by Order and Cart model, that we want to sync."""
        return {
            'total_price': self.total_price,
            'total_price_net': self.total_price_net,
            'region_total_price': self.region_total_price,
            'region_total_price_net': self.region_total_price_net,
            'promo_amount': self.promo_amount,
            'promo_amount_net': self.promo_amount_net,
            'region_promo_amount': self.region_promo_amount,
            'region_promo_amount_net': self.region_promo_amount_net,
            'assembly': self.assembly,
            'region_vat': self.region_vat,
            'vat_type': self.vat_type,
            'vat_rate': self.vat_rate,
            'region': self.region,
            'used_promo': self.used_promo,
            'country': self.region.name,  # fuck it
            # why not region.country.name? because this is easier and all countries have
            # the same name as region
        }

    @property
    def is_france(self):
        return self.region.name == Countries.france.name

    def get_base_total_value(self) -> Decimal:
        if not self.total_price:
            return Decimal(0)

        return self.total_price

    def get_base_total_value_net(self) -> Decimal:
        if not self.total_price_net:
            return Decimal(0)

        return self.total_price_net

    def get_total_value(self) -> Decimal:
        if self.region_total_price is None:
            return self.get_base_total_value()

        return self.region_total_price

    def get_total_value_net(self) -> Decimal:
        if self.region_total_price_net is None:
            return self.get_base_total_value_net()

        return self.region_total_price_net

    def get_total_price(self) -> str:
        return self.display_regionalized(self.get_total_price_number())

    @abstractmethod
    def get_total_price_number(self) -> Decimal:
        pass

    def get_base_vat_value(self) -> Decimal:
        return self.get_base_total_value() - self.get_base_total_value_net()

    def get_vat_value(self) -> Decimal:
        return self.get_total_value() - self.get_total_value_net()

    @cached_property
    def paid_assembly_items(self):
        return self.items.exclude(free_assembly_service=True)

    def get_assembly_price(self) -> int:
        return sum(
            item.aggregate_region_assembly_price or item.aggregate_assembly_price
            for item in self.paid_assembly_items
        )

    def get_assembly_price_in_euro(self) -> int:
        return sum(item.aggregate_assembly_price for item in self.paid_assembly_items)

    def get_delivery_price(self) -> int:
        return sum(
            item.aggregate_region_delivery_price or item.aggregate_delivery_price
            for item in self.items.all()
        )

    def get_delivery_price_in_euro(self) -> int:
        return sum(item.aggregate_delivery_price for item in self.items.all())

    def get_delivery_region_price_promo_value(self) -> Decimal:
        return Decimal(
            sum(item.aggregate_region_delivery_promo_value for item in self.items.all())
        )

    def get_delivery_price_without_promo(self) -> Decimal:
        # in EUR
        return Decimal(
            sum(
                item.aggregate_delivery_price_without_discount
                for item in self.items.all()
            )
        )

    def get_base_promo_amount(self) -> Decimal:
        if not self.promo_amount:
            return Decimal(0)

        return self.promo_amount

    def get_base_promo_amount_net(self) -> Decimal:
        if not self.promo_amount_net:
            return Decimal(0)

        return self.promo_amount_net

    @abstractmethod
    def clear_promo(self, commit: bool = True) -> None:
        pass

    def clear_promo_amounts(self, save: bool = False) -> None:
        self.promo_amount = Decimal(0)
        self.promo_amount_net = Decimal(0)
        self.region_promo_amount = Decimal(0)
        self.region_promo_amount_net = Decimal(0)

        if save:
            self.save(
                update_fields=[
                    'promo_amount',
                    'promo_amount_net',
                    'region_promo_amount',
                    'region_promo_amount_net',
                ]
            )

    # TODO: rename and make a property?
    def get_total_price_number_before_discount(self) -> Decimal:
        if not self.items.exists():
            return Decimal(0)

        promo_amount = self.region_promo_amount or 0
        result = self.get_total_price_number() + promo_amount

        if self.assembly is True:
            result -= self.get_assembly_price()

        return result

    @property
    def possible_shelf_assembly(self):
        assembly_possible = False

        for item in self.material_items.all():
            if item.content_type.model == SellableItemContentTypes.SAMPLE_BOX:
                continue

            if item.sellable_item.shelf_type not in (ShelfType.TYPE03, ShelfType.SOFA_TYPE01):
                assembly_possible = True

        return (
            assembly_possible
            and LimitationService(region=self.region).is_assembly_available
        )

    @cached_property
    def get_recycle_tax_value(self):
        if not self.is_france:
            return 0

        tax_value = 0
        for order_item in self.items.all():
            tax_value += order_item.recycle_tax_value
        return tax_value

    @property
    def is_vat_exempt(self):
        return self.vat_type in {
            VatType.PL_EXPORT_0.legacy_type,
            VatType.PL_WDT_0.legacy_type,
        }

    @property
    def is_from_non_eu_country(self):
        """
        For these countries we do not deduct VAT from the gross price,
        even though they are outside the EU and they shall be exempt from VAT.
        Like a Robin Hood, but we do not give to the poor.

        TODO: when old VAT values, const will be removed, it may be equal
         to having VAT EXPORT as a vat type.
        """
        return self.region.name in {
            Countries.united_kingdom.name,
            Countries.norway.name,
            Countries.switzerland.name,
        }

    @abstractmethod
    def calculate_total_price_for_items(
        self,
        items: QuerySet['CartItem'] | QuerySet['OrderItem'],
    ) -> Decimal:
        pass

    def set_vat_fields(self) -> None:
        self.vat_rate = VatDetailsGetter(self).get_vat_rate_for_display()
        country = self.region.country
        if country and country.region_vat:
            self.region_vat = True

    def get_items_for_voucher(self) -> list['OrderItem']:
        if not self.used_promo:
            return []
        return list(self.used_promo.get_promotion_affected_items(self))

    def is_free(self) -> bool:
        if self.get_total_value() == 0:
            return True
        elif self.is_full_barter_deal():
            return True
        return False

    @abstractmethod
    def is_full_barter_deal(self) -> bool:
        pass

    @mark_safe
    def get_items_as_string(self):
        resp = ''

        for item in self.items.all():
            if item.sellable_item is not None:
                if item.content_type == SellableItemContentTypes.SAMPLE_BOX:
                    resp += format_html(
                        f'{item.sellable_item.get_variant_name()} '
                        f'({item.object_id}) - {item.get_price()}<br/>'
                    )
                else:
                    resp += format_html(
                        f'{item.sellable_item.get_pretty_id_name()} '
                        f'({item.object_id}) - {item.get_price()}<br/>'
                    )
        return resp

    @property
    def aggregate_items_count(self) -> int:
        return self.items.aggregate(
            count_with_quantity=Coalesce(Sum('quantity'), Value(0))
        )['count_with_quantity']

    def get_region_aggregated_furniture_price(self):
        # no assembly, no delivery, no extra services, just items in regional currency
        return sum(
            item.aggregate_region_price
            for item in self.items.exclude(content_type__model='additionalservice')
        )

    def get_old_sofa_collection_price(self) -> Decimal:
        from services.services.old_sofa_collection import OldSofaCollectionService

        if not self.has_s01:
            return Decimal('0')

        gross_price = OldSofaCollectionService(self).calculate_price()
        if self.is_vat_exempt and not self.is_from_non_eu_country:
            return gross_price / (1 + self.region.country.vat)

        return gross_price

    def get_white_gloves_delivery_price(self) -> Decimal:
        from services.services.white_gloves_delivery import WhiteGlovesDeliveryService

        gross_price = WhiteGlovesDeliveryService(self).calculate_price()
        if self.is_vat_exempt and not self.is_from_non_eu_country:
            return gross_price / (1 + self.region.country.vat)

        return gross_price


class ItemPriceAbstractModel(models.Model):
    """An abstract model for price-related fields and methods.

    Used by OrderItem and CartItem models.
    """

    # how much company earns in EUR on this Item
    price = models.DecimalField(max_digits=12, decimal_places=2)
    price_net = models.DecimalField(max_digits=12, decimal_places=2)
    # how much customer pays in currency
    region_price = models.DecimalField(max_digits=12, decimal_places=2)
    region_price_net = models.DecimalField(max_digits=12, decimal_places=2)
    assembly_price = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    region_assembly_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
    )
    delivery_price = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    region_delivery_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
    )
    vat_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    region_vat_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # NOTE: value needed for invoicing, prices above don't include promo
    region_promo_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    # similar case like field above, value needed for invoicing,
    region_delivery_promo_value = models.DecimalField(
        max_digits=12, decimal_places=2, default=0
    )
    recycle_tax_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # fields that price depends on
    free_assembly_service = models.BooleanField(null=True)

    # relations that price depends on
    region = models.ForeignKey('regions.Region', null=True, on_delete=models.SET_NULL)

    class Meta:
        abstract = True

    @property
    def common_fields(self) -> dict:
        """Fields shared by OrderItem and CartItem model, that we want to sync."""
        return {
            'price': self.price,
            'price_net': self.price_net,
            'region_price': self.region_price,
            'region_price_net': self.region_price_net,
            'assembly_price': self.assembly_price,
            'region_assembly_price': self.region_assembly_price,
            'delivery_price': self.delivery_price,
            'region_delivery_price': self.region_delivery_price,
            'region_delivery_promo_value': self.region_delivery_promo_value,
            'vat_amount': self.vat_amount,
            'region_vat_amount': self.region_vat_amount,
            'region_promo_value': self.region_promo_value,
            'recycle_tax_value': self.recycle_tax_value,
            'free_assembly_service': self.free_assembly_service,
            'region': self.region,
        }

    # TODO: not used anywhere, to delete
    @classmethod
    def get_pricing_fields(cls) -> list:
        return [
            'price',
            'price_net',
            'region_price',
            'region_price_net',
            'assembly_price',
            'region_assembly_price',
            'delivery_price',
            'region_delivery_price',
            'vat_amount',
            'region_vat_amount',
            'recycle_tax_value',
            'free_assembly_service',
            'region',
        ]

    @property
    @abstractmethod
    def parent(self) -> Union['Order', 'Cart']:
        pass

    def get_price_number(self) -> Decimal:
        if self.region_price == 0:
            return self.price
        return self.region_price

    def get_price_net_number(self) -> Decimal:
        if self.region_price_net == 0:
            return self.price_net
        return self.region_price_net

    def get_base_price_display(self):
        return format_price(self.price)

    def get_base_price_net_display(self):
        return format_price(self.price_net)

    def get_price(self):
        if self.region_price == 0:
            return self.get_base_price_display()
        return self.display_regionalized(self.region_price)

    def get_price_net(self):
        if self.region_price_net == 0:
            return self.get_base_price_net_display()
        return self.display_regionalized(self.region_price_net)

    def get_delivery_price(self) -> Decimal:
        if self.region_delivery_price == 0:
            return self.delivery_price
        return self.region_delivery_price

    @property
    def delivery_price_without_discount(self) -> Decimal:
        return self.sellable_item.get_delivery_price(self.region)

    def get_assembly_price(self) -> Decimal:
        if self.region_assembly_price == 0:
            return self.assembly_price
        return self.region_assembly_price

    def get_vat_amount(self) -> Decimal:
        if self.region_vat_amount == 0:
            return self.vat_amount
        return self.region_vat_amount

    def get_base_price_without_discount(self) -> Decimal:
        siblings = self.parent.items.all()
        promo_amount_even = self.parent.get_base_promo_amount() / siblings.count()
        value = self.price - promo_amount_even
        return value if value > 0 else Decimal('0')

    def get_base_price_net_without_discount(self) -> Decimal:
        siblings = self.parent.items.all()
        promo_amount_even = self.parent.get_base_promo_amount_net() / siblings.count()
        value = self.price_net - promo_amount_even
        return value if value > 0 else Decimal('0')
