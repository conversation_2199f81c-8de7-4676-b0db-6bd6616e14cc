import typing

from decimal import Decimal

from .coefficients import get_coefficients

if typing.TYPE_CHECKING:
    from gallery.models import Sotty


def calculate_elements_price(
    sotty: 'Sotty',
    region_name: str,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    from gallery.models import Sotty

    is_corduroy = sotty.fabric == Sotty.Fabric.CORDUROY
    material_suffix = '_CORDUROY' if is_corduroy else '_REWOOL'
    cover_suffix = '_COVER' if sotty.covers_only else ''
    fireproof_suffix = ''
    if not cover_suffix and region_name == 'united_kingdom' and not is_corduroy:
        fireproof_suffix = '_FIREPROOF'
    elements = (
        sotty.armrests
        + sotty.chaise_longues
        + sotty.corners
        + sotty.footrests
        + sotty.seaters
    )
    price = Decimal(0)
    for element in elements:
        coef_name = f'{element["id"]}{material_suffix}{cover_suffix}{fireproof_suffix}'
        price += price_coefficients.get(coef_name)
    return price


def calculate_price(sotty: 'Sotty', region_name: str, pricing_version=None) -> int:
    if pricing_version is None:
        price_coefficients = get_coefficients(region_name)
    else:
        price_coefficients = pricing_version.coefficients
    price = calculate_elements_price(sotty, region_name, price_coefficients)
    return int(price)
