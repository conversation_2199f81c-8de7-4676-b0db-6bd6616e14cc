# Setup backend
 - [First steps](#first-setup-steps)
    - [Setup with docker](#docker)
    - [Setup without docker](#without-docker)
 - [Next setup steps](#common)
 - [Running tests](#testing)
 - [Optional env variables](#env-variables-necessary-for-specific-purposes)

## First setup steps

### Docker

[Docker setup instructions](../.devops/docker/README.md)

### Without docker

#### MAC / MAC M1 - install dependencies
```
brew install zlib
brew install jpeg
brew install libmagic
```

#### Environment
Before running backend populate environment variable file:

```bash
cp .env.dist src/.env
```

then set the environment variable on your computer, to read the `.env` file:

On macOS add the following line to `~/.zshrc` (if the file does not exist create it):

```
export DJANGO_READ_DOT_ENV_FILE=1
```

On Linux:

```
echo "export DJANGO_READ_DOT_ENV_FILE=1">>~/.bashrc
```

#### Pyenv

> [`pyenv`](https://github.com/pyenv/pyenv) is recommended to
> manage Python's versions.

Install pyenv:
```bash
curl https://pyenv.run | bash
```

Install our current python version:

```
pyenv install 3.11.9
```


EITHER:
Set our current version as a global python version (recommended) and reset shell:
```
pyenv global 3.11.9
```
OR:
Set our python version as a current version for this shell:
```
pyenv shell 3.11.9
```

#### Virtualenv

 + Make sure you're using correct python version:
    ```python --version``` should display 3.11
 + Create the venv in the selected directory (i.e. inside the repo):
    ```python -m venv venv```
 + Activate your venv:
    ```source venv/bin/activate```

#### Install `poetry` and requirements

To [install `poetry`](https://python-poetry.org/docs/#installation) use one of
following commands:

```bash
curl -sSL https://install.python-poetry.org | POETRY_VERSION=2.1.2 python3 -
```

or

```bash
pip install --upgrade poetry==2.1.2
```

Check if `poetry` is installed properly by running:

```bash
poetry --version
```

`poetry` is installed, so it's time to install all dependencies with following
command:

```bash
poetry install
```

`poetry` has really rich set of commands that will make your life easier -
please read [documentation](https://python-poetry.org/docs/cli/).

For instance, to activate virtual environment with previously installed
dependencies simply run:

```bash
poetry shell
```

If you only want to quickly run some command in virtual environment:

```bash
poetry run pytest
```

#### git pre-commit hooks

Set up few formatters and linters to be run on every `git commit` via git hooks:

```
# in project's root directory
pre-commit install --install-hooks
```

Every staged file will be checked by those linters. In case of any error, `git commit` will fail and list all problems. Resolve those issues and `git commit` again.

#### Install and run postgres

##### MAC
```
brew install postgresql@14
brew services start postgresql@14
```

##### MAC - Add postgresql PATH (.zshrc)
For Intel chipsets:
```
export PATH="/usr/local/opt/postgresql@14/bin:$PATH"
```

For M1 chipsets:
```
export PATH="/opt/homebrew/opt/postgresql@14/bin:$PATH"
```

##### Ubuntu
```
sudo apt install postgresql-14 postgresql-contrib
```
#### Create the database and role
```
psql -U system_username
```
or ```psql postgres``` and in the postgres console:
  ```
  CREATE DATABASE cstm;
  CREATE ROLE cstm;
  ```

  + verify db is visible in `\l`
  + exit console: `\q`,

**WARNING: If on macOS something is still not right, use the system user**

If you encounter problems during Database dump: ``` ROLE cstm does not exists!```, you will probably need to add a "cstm" role:

use ``` psql postgres ``` and in the postgres console:

```
CREATE ROLE "cstm";
ALTER ROLE "cstm" WITH LOGIN;
ALTER ROLE "cstm" WITH SUPERUSER;
```

This will allow to import database dump into your local database.

#### Install redis
##### MAC
```
brew install redis
brew services start redis
```
##### Ubuntu
```
sudo apt install redis
```

## Common
### Required .env variables
For basic local development only one variable needs to be set:

```
PRODUCTION_SYSTEM_TOKEN=
```

### Database dump

`Keywords: load_mini_db.sh load_mini_db minidb mini_db load mini db`

https://cstm-tasks.atlassian.net/wiki/spaces/~816323402/pages/2239529004/Load+mini+db+script

### Migrations
`src/manage.py migrate`

### Add admin role
`src/manage.py createsuperuser`

### Regenerate DNA entries
`src/manage.py regenerate_dna_entries`

### Run the backend
`src/manage.py runserver`

### Are You PAPA developer wanting to devlop CSTM locally?
1. You are clearly lost
2. set `PRODUCTION_SYSTEM_API_URL=http://production-system-app:8000/api/v1` in .env
3. set `PRODUCTION_SYSTEM_LOCAL_URL=http://localhost:8001` (or whatever port you have PS running on) in .env
4. set `PRODUCTION_SYSTEM_TOKEN=[set if to whatever You created as AuthToken in Your local PS]`
5. set `CSTM_PROD_TOKEN=[ask for this on slack, needed to update PFs locally]`
6. set `CSTM_CALLBACK_URL=http://localhost:8000`


### Changes in pricing
1. Make sure a business person gave you the
      coefficients and explained which changes to apply
2. Before you make any changes, cd to `pricing_v3` directory
      and run `./versioning/create_new_version.sh <year> <month> <day>`
      where year, month and day are the date when the change will be applied
3. this should create a new version of the pricing in the `history` directory and a new migration
4. Apply your changes and commit them


### Add Translations
[Tutorial - Atlassian ](https://cstm-tasks.atlassian.net/wiki/spaces/FRON/pages/551059469/Klucze+translacji+T+umaczenia)

## Testing
**When working with `docker` - add `docker-compose run --rm app` at the beginning of each command**

To run basic test suite use one of following commands:

```bash
pytest
```

+ when all tests are required, override markers expression from
`setup.cfg` with empty one:

```bash
 pytest -m=""
```

or use `--run-all-tests` cli option:

```bash
pytest --run-all-tests
```

+ when coverage report is required:

```bash
pytest --cov=.
```

The command listed above will generate a term and XML report located in
`src/.test_reports/` directory, to generate HTML report use following flags:

```bash
pytest --cov=. --cov-report=html --cov-report=xml
```

## Env variables necessary for specific purposes

##### Facebook

+ `FB_APP_ID`
+ `FB_APP_SECRET`
+ `FB_APP_TOKEN`

##### Mailchimp/Mandrill

+ `MAILCHIMP_API_KEY`

To use Mandrill email backend please refer to production settings and remember
to set `DJANGO_EMAIL_*` environment variables to proper values, because
Mandrill is used via SMPT in production environment.

##### Trusted shops

+ `TRUSTEDSHOPS_TS_ID`
+ `TRUSTEDSHOPS_USER`
+ `TRUSTEDSHOPS_PASSWORD`

##### Google Analytics

+ `GA_ACCOUNT`
+ `GA_ACCOUNT_KEY_PATH`

##### Slack

+ `SLACK_CS_WEBHOOK_URL`

##### Typeform

+ `TYPEFORM_TOKEN`

##### Adyen

+ `ADYEN_MERCHANT_ACCOUNT`
+ `ADYEN_MERCHANT_SECRET`
+ `ADYEN_MERCHANT_WS_ACCOUNT`
+ `ADYEN_MERCHANT_WS_SECRET`
+ `ADYEN_PUBLIC_KEY`
+ `ADYEN_REPORT_USER`
+ `ADYEN_REPORT_PASSWORD`
+ `ADYEN_HMAC_KEY`
+ `CHECKOUT_API_KEY`

##### Production system

+ `PRODUCTION_SYSTEM_API_URL`
+ `PRODUCTION_SYSTEM_URL`

##### Local S3 storage - minio

+ `USE_AWS_S3_MEDIA_STORAGE=True`
+ `MINIO_ROOT_USER`
+ `MINIO_ROOT_PASSWORD`
+ `LOCAL_BUCKET_NAME`
+ `LOCAL_STORAGE_URL`
+ `DEFAULT_FILE_STORAGE=storages.backends.s3boto3.S3Boto3Storage`


## Black - git blame/annotate

This project was black-formatted. For a cleaner history run `git config blame.ignoreRevsFile .ignoreRevsFile`

## Minio - local s3 storage

To run minio (local S3 storage) locally you need to install it:
https://docs.min.io/docs/minio-quickstart-guide.html.
Set your root credentials (default `minioadmin:minioadmin`) to env variables
(`MINIO_ROOT_USER` and `MINIO_ROOT_PASSWORD`).

Run your minio in console and go to the http://127.0.0.1:9000
(copy it to `LOCAL_STORAGE_URL` env variable) and log in using
root credentials.

Create new bucket using dashboard in the browser - copy name of the bucket to the
`LOCAL_BUCKET_NAME` env variable.

You also need to copy and uncomment env variables listed in `.env.dist` file
in the section for local S3 bucket.

## REST API - Docs

We use drf-spectator in order to generate API documentation.

Because for ecommerce project we use CamelCase convention and for CSTM
snake_case we split documentation based on that.

    Complete ecommerce documentation is availabe under these urls:
        /api/v1/ecommerce/docs/
        /api/v1/ecommerce/docs/redoc/
        /api/v1/ecommerce/docs/swagger/

    Complete CSTM documentation is availabe under these urls:
        /api/v1/docs/
        /api/v1/docs/redoc/
        /api/v1/docs/swagger/
