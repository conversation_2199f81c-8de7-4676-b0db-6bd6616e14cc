from django.contrib.auth.views import (
    LoginView,
    LogoutView,
)
from django.urls import (
    path,
    re_path,
)

from complaints.views import (
    AddComplaintView,
    DeleteComplaintView,
    UpdateComplaintView,
)
from customer_service.views import (
    AbortOrderView,
    AddSampleBoxView,
    AssemblyInstructionView,
    ChangeIsBusinessTypeView,
    ChangeOrderOwnerView,
    ChangeOrderStatusView,
    ChangeOrderToPending,
    ChangeProductPriorityView,
    ChangeSourcePriorityView,
    ChangeUserLangView,
    CreateDamageFormView,
    CreateFreeReturnView,
    CreateKlarnaAdjustmentView,
    CreateProformaAndSendView,
    CustomerContactDetailView,
    CustomerContactView,
    CustomerReviewsView,
    CustomerServiceCorrectionList,
    CustomerServiceDashboardView,
    CustomerServicePendingOrders,
    CustomerServicePromoPaidOrders,
    CustomerServiceSearchView,
    DeactivateUserAccountView,
    DesignChangedView,
    EditProformaView,
    GenerateInstructionsView,
    GetCostWithTransport,
    MarginCalculatorView,
    NewsletterUnsubscribeFormView,
    OrderAddExtraDiscountRecalculationsView,
    OrderAddExtraDiscountRollbackRecalculationsView,
    OrderAddExtraDiscountSummaryView,
    OrderAddExtraDiscountWizardView,
    PromocodeAddView,
    PushToProductionView,
    RegionPriceCalculatorView,
    SendInvoiceView,
    SendOrderNotificationsFormView,
    StopMailingFlowView,
    UnsuccessfulPaymentsView,
    UpdateOrderNotesView,
    UpdateOrderView,
    UserOverviewView,
)
from customer_service.views_correction_requests import (
    NeutralizeInvoiceView,
    RequestCorrectionAddressView,
    RequestCorrectionInvoiceItemView,
    RequestCorrectionView,
)
from customer_service.views_switch import (
    OrderSwitchItemOnHoldView,
    OrderSwitchItemReplacementView,
    OrderSwitchRecalculationsView,
    OrderSwitchSummaryView,
    OrderSwitchWaitingForAdditionalPaymentsView,
    OrderSwitchWizardView,
)

urlpatterns = [
    re_path(r'^$', CustomerServiceDashboardView.as_view(), name='cs_dashboard'),
    re_path(
        r'^login/$',
        LoginView.as_view(template_name='customer_service/login.html'),
        name='cs_login',
    ),
    re_path(r'^logout/$', LogoutView.as_view(next_page='/cs/'), name='cs_logout'),
    re_path(r'^search/?$', CustomerServiceSearchView.as_view(), name='cs_search'),
    re_path(
        r'^request_correction/(?P<pk>.*)/$',
        RequestCorrectionView.as_view(),
        name='cs_request_correction',
    ),
    re_path(
        r'^request_correction_invoice_item/(?P<pk>.*)/$',
        RequestCorrectionInvoiceItemView.as_view(),
        name='cs_request_correction_invoice_item',
    ),
    re_path(
        r'^request_correction_address/(?P<pk>.*)/$',
        RequestCorrectionAddressView.as_view(),
        name='cs_request_correction_address',
    ),
    re_path(
        r'^pending_orders/?$',
        CustomerServicePendingOrders.as_view(),
        name='cs_pending_orders_list',
    ),
    re_path(
        r'^promo_paid_orders/?$',
        CustomerServicePromoPaidOrders.as_view(),
        name='cs_promo_paid_orders_list',
    ),
    re_path(
        r'^correction_requests/?$',
        CustomerServiceCorrectionList.as_view(),
        name='cs_pending_correction_list',
    ),
    path(
        'get_cost_with_transport/<int:product_id>/',
        GetCostWithTransport.as_view(),
        name='get_cost_with_transport',
    ),
    re_path(
        r'^user_overview/(?P<pk>.*)/$',
        UserOverviewView.as_view(),
        name='cs_user_overview',
    ),
    re_path(
        r'^update_order/(?P<pk>.*)/$', UpdateOrderView.as_view(), name='cs_update_order'
    ),
    path(
        'create_damage_form/<int:pk>/',
        CreateDamageFormView.as_view(),
        name='cs_create_damage_form',
    ),
    re_path(
        r'^change_order_to_pending/(?P<pk>.*)/$',
        ChangeOrderToPending.as_view(),
        name='cs_change_order_to_pending',
    ),
    re_path(
        r'^create_and_send_proforma/(?P<pk>.*)/$',
        CreateProformaAndSendView.as_view(),
        name='cs_sent_proforma',
    ),
    path(
        'design_changed/<int:pk>/',
        DesignChangedView.as_view(),
        name='cs_design_changed',
    ),
    path(
        'regenerate_manual/<int:pk>/',
        AssemblyInstructionView.as_view(),
        name='regenerate_assembly_manual',
    ),
    re_path(
        r'^create_proforma/(?P<pk>.*)/$',
        CreateProformaAndSendView.as_view(),
        {'dont_sent': True},
        name='cs_create_proforma',
    ),
    re_path(
        r'^update_order_notes/(?P<pk>.*)/$',
        UpdateOrderNotesView.as_view(),
        name='cs_update_notes',
    ),
    re_path(
        r'^change_order_owner/(?P<pk>.*)/$',
        ChangeOrderOwnerView.as_view(),
        name='cs_change_order_owner',
    ),
    path(
        'change_product_priority/<int:pk>/<int:priority>/',
        ChangeProductPriorityView.as_view(),
        name='change_product_priority',
    ),
    path(
        'change_source_priority/<int:pk>/<int:priority>/',
        ChangeSourcePriorityView.as_view(),
        name='change_source_priority',
    ),
    path(
        'abort_order/<int:pk>/',
        AbortOrderView.as_view(),
        name='abort_order',
    ),
    path(
        'change_user_profile_is_business_type/<int:pk>/',
        ChangeIsBusinessTypeView.as_view(),
        name='change_user_profile_is_business_type',
    ),
    re_path(
        r'^send_invoice_again/(?P<pk>.*)/$',
        SendInvoiceView.as_view(),
        name='cs_sent_invoice_again',
    ),
    re_path(r'^add_sample_box/$', AddSampleBoxView.as_view(), name='cs_add_sample_box'),
    re_path(
        r'^create_promocode/$',
        PromocodeAddView.as_view(),
        name='cs_create_promocode',
    ),
    re_path(
        r'^change_user_lang/(?P<user_id>.*)/$',
        ChangeUserLangView.as_view(),
        name='change_user_lang',
    ),
    path(
        'deactivate_user_account/<int:pk>/',
        DeactivateUserAccountView.as_view(),
        name='deactivate_user_account',
    ),
    re_path(
        r'^change_order_status/(?P<order_id>.*)/$',
        ChangeOrderStatusView.as_view(),
        name='cs_change_order_status',
    ),
    re_path(
        r'^stop_mailing_flow/(?P<pk>.*)/$',
        StopMailingFlowView.as_view(),
        name='cs_stop_mailing_flow',
    ),
    re_path(
        r'^calculator/', RegionPriceCalculatorView.as_view(), name='region_calculator'
    ),
    re_path(
        r'^edit_proforma/(?P<pk>.*)/$',
        EditProformaView.as_view(),
        name='cs_edit_proforma',
    ),
    re_path(
        r'^unsuccessful_payments/?$',
        UnsuccessfulPaymentsView.as_view(),
        name='cs_unsuccessful_payments',
    ),
    path(
        'create_free_return/<int:pk>/',
        CreateFreeReturnView.as_view(),
        name='cs_create_free_return',
    ),
    re_path(r'^reviews/?$', CustomerReviewsView.as_view(), name='cs_reviews'),
    re_path(
        r'^margin_calculator/?$',
        MarginCalculatorView.as_view(),
        name='cs_margin_calculator',
    ),
    path(
        'customer_contact/',
        CustomerContactView.as_view(),
        name='cs_customer_contact',
    ),
    path(
        'customer_contact/<int:pk>/',
        CustomerContactDetailView.as_view(),
        name='cs_customer_contact_detail',
    ),
    re_path(
        r'^neutralize_invoice/(?P<pk>.*)',
        NeutralizeInvoiceView.as_view(),
        name='cs_neutralize_invoice',
    ),
    re_path(
        r'^push_to_production/(?P<pk>.*)/$',
        PushToProductionView.as_view(),
        name='cs_push_to_production',
    ),
    re_path(
        r'^generate_instructions/(?P<pk>.*)/$',
        GenerateInstructionsView.as_view(),
        name='cs_generate_instructions',
    ),
    path(
        'send_order_notifications/<int:pk>/',
        SendOrderNotificationsFormView.as_view(),
        name='cs_send_order_notifications',
    ),
    path(
        'order_switch/<int:order_id>/',
        OrderSwitchWizardView.as_view(),
        name='cs_order_switch',
    ),
    path(
        'order_switch/item_on_hold/<int:order_id>/',
        OrderSwitchItemOnHoldView.as_view(),
        name='cs_order_switch_item_on_hold',
    ),
    path(
        'order_switch/item_replacement/<int:order_id>/',
        OrderSwitchItemReplacementView.as_view(),
        name='cs_order_switch_item_replacement',
    ),
    path(
        'order_switch/order_recalculations/<int:order_id>/',
        OrderSwitchRecalculationsView.as_view(),
        name='cs_order_switch_order_recalculations',
    ),
    path(
        'order_switch/process_additional_payment/<int:order_id>/',
        OrderSwitchWaitingForAdditionalPaymentsView.as_view(),
        name='cs_order_switch_commit_additional_payment',
    ),
    path(
        'order_switch/summary/<int:order_id>/',
        OrderSwitchSummaryView.as_view(),
        name='cs_order_switch_summary_view',
    ),
    path(
        'order_add_extra_discount/<int:order_id>/',
        OrderAddExtraDiscountWizardView.as_view(),
        name='cs_order_add_extra_discount',
    ),
    path(
        'order_add_extra_discount/discount_recalculation/<int:order_id>/',
        OrderAddExtraDiscountRecalculationsView.as_view(),
        name='cs_order_add_extra_discount_recalculation',
    ),
    path(
        'order_add_extra_discount/rollback_discount_recalculation/<int:order_id>/',
        OrderAddExtraDiscountRollbackRecalculationsView.as_view(),
        name='cs_order_add_extra_discount_rollback_recalculation',
    ),
    path(
        'order_add_extra_discount/summary/<int:order_id>/',
        OrderAddExtraDiscountSummaryView.as_view(),
        name='cs_order_add_extra_discount_summary',
    ),
    path(
        'newsletter/unsubscribe/',
        NewsletterUnsubscribeFormView.as_view(),
        name='cs_newsletter_unsubscribe',
    ),
    path(
        'klarna_adjustment/<int:invoice_id>/',
        CreateKlarnaAdjustmentView.as_view(),
        name='cs_klarna_adjustment',
    ),
    path(
        'create_complaint/<int:pk>/',
        AddComplaintView.as_view(),
        name='cs_create_complaint',
    ),
    path(
        'update_complaint/<int:pk>/',
        UpdateComplaintView.as_view(),
        name='cs_update_complaint',
    ),
    path(
        'delete_complaint/<int:pk>/',
        DeleteComplaintView.as_view(),
        name='cs_delete_complaint',
    ),
]
