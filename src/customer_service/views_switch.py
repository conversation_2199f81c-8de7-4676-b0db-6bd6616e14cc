import json

from django.contrib import messages
from django.shortcuts import (
    get_object_or_404,
    redirect,
)
from django.urls import (
    reverse,
    reverse_lazy,
)
from django.views.generic import (
    FormView,
    TemplateView,
)

from customer_service.api.serializers import ProductWithPrioritiesSwitchSerializer
from customer_service.forms import (
    OrderSwitchItemOnHoldForm,
    OrderSwitchItemReplacementForm,
    OrderSwitchRecalculationsForm,
)
from customer_service.views import (
    CustomerServiceMixinView,
    WizardMixinView,
)
from orders.models import (
    Order,
    OrderItem,
)
from orders.switch_status import (
    SwitchStatus,
    is_order_promo_inactive,
)


class OrderSwitchLogCustomerServiceViewMixin:
    def log_cs_order_switch_status_change(self, order: Order, data: dict | None = None):
        self.log_cs_data(
            self.request,
            self.STEP_NAME,
            order._meta.label,
            order.pk,
            data,
            additional_info=None,
        )


class OrderSwitchWizardView(Wizard<PERSON><PERSON>inVie<PERSON>, TemplateView):
    STATUS_TO_VIEW = {
        SwitchStatus.BLANK.value: 'cs_order_switch_item_on_hold',
        SwitchStatus.ITEM_REPLACEMENT.value: 'cs_order_switch_item_replacement',
        SwitchStatus.COST_RECALCULATION.value: 'cs_order_switch_order_recalculations',
        SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value: 'cs_order_switch_commit_additional_payment',  # noqa: E501
        SwitchStatus.COMPLETED.value: 'cs_order_switch_summary_view',
        SwitchStatus.CANCELLED.value: 'cs_order_switch_item_on_hold',
    }

    def get_step(self, order: 'Order') -> str:
        return self.STATUS_TO_VIEW[order.switch_status]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        source_product = order.source_order_item.product_set.all_with_deleted().first()
        serialized_product = ProductWithPrioritiesSwitchSerializer(source_product).data
        context['serialized_source_product'] = json.dumps(serialized_product)
        context['source_product'] = serialized_product
        return context


class OrderSwitchItemOnHoldView(
    OrderSwitchLogCustomerServiceViewMixin,
    CustomerServiceMixinView,
    FormView,
):
    STEP_NAME = 'cs_order_switch_item_on_hold'

    success_url = 'cs_order_switch_item_replacement'
    form_class = OrderSwitchItemOnHoldForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        context['order'] = order
        return context

    def get_success_url(self):
        return reverse_lazy(
            self.success_url, kwargs={'order_id': self.kwargs.get('order_id')}
        )

    def form_valid(self, form):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        source_order_item = get_object_or_404(
            OrderItem,
            pk=form.cleaned_data['source_order_item'],
        )
        order.commit_item_on_hold(source_order_item, by=self.request.user)
        order.save()
        self.log_cs_order_switch_status_change(
            order, {'source_order_item': source_order_item.pk}
        )
        self.display_message(order, source_order_item)
        return super().form_valid(form)

    def display_message(self, order: Order, source_order_item: OrderItem):
        messages.success(
            self.request,
            f'Product for {source_order_item} successfully changed status to on hold',
        )

        if is_order_promo_inactive(order):
            messages.error(
                self.request,
                f'Promocode: {order.used_promo.code} which is not active anymore '
                'was applied to this order. Please activate this coupon for purpose '
                'of this switch or handle it manually',
            )


class OrderSwitchItemReplacementView(
    OrderSwitchLogCustomerServiceViewMixin,
    OrderSwitchWizardView,
    CustomerServiceMixinView,
    FormView,
):
    STEP_NAME = 'cs_order_switch_item_replacement'

    template_name = 'customer_service/order_switch/item_replacement.html'
    form_class = OrderSwitchItemReplacementForm

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        self.order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        form_kwargs['source_order_item'] = self.order.source_order_item
        return form_kwargs

    def get_success_url(self):
        return reverse_lazy(
            'cs_order_switch_order_recalculations',
            kwargs={'order_id': self.kwargs.get('order_id')},
        )

    def form_valid(self, form):
        furniture_to_add = form.cleaned_data['furniture_to_add']
        quantity = form.cleaned_data['quantity']

        message_status, message_text = self.order.commit_item_replacement(
            furniture_to_add,
            quantity=quantity,
            by=self.request.user,
        )
        self.order.save()
        self.log_cs_order_switch_status_change(
            self.order, {'furniture_to_add': furniture_to_add.pk}
        )
        self.display_message(message_status, message_text)
        return super().form_valid(form)

    def display_message(self, message_status, message_text):
        if message_status == self.MESSAGE_ERROR_STATUS:
            messages.error(self.request, message_text)
        else:
            messages.success(self.request, 'Please check price differences')


class OrderSwitchRecalculationsView(
    OrderSwitchLogCustomerServiceViewMixin,
    OrderSwitchWizardView,
    CustomerServiceMixinView,
    FormView,
):
    STEP_NAME = 'cs_order_switch_order_recalculations'

    template_name = 'customer_service/order_switch/order_recalculations.html'
    form_class = OrderSwitchRecalculationsForm

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        self.order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        form_kwargs['order'] = self.order
        return form_kwargs

    def get_success_url(self):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        kwargs = {'order_id': self.kwargs.get('order_id')}
        if order.should_wait_for_additional_payment():
            return reverse('cs_order_switch_commit_additional_payment', kwargs=kwargs)
        return reverse('cs_order_switch_summary_view', kwargs=kwargs)

    def form_valid(self, form):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        options = form.cleaned_data
        order.commit_recalculations(options, by=self.request.user, description=options)
        order.save()
        self.log_cs_order_switch_status_change(self.order, options)
        self.display_message(order)
        return super().form_valid(form)

    def display_message(self, order):
        if order.message_status == self.MESSAGE_ERROR_STATUS:
            messages.error(self.request, order.message_text)
        elif order.should_wait_for_additional_payment():
            messages.info(
                self.request,
                f'Waiting for additional payment of {order.total_price_change} '
                f'product for {order.target_order_item} successfully changed '
                'status to on hold',
            )
        else:
            messages.success(self.request, 'Project has been changed')


class OrderSwitchWaitingForAdditionalPaymentsView(
    OrderSwitchLogCustomerServiceViewMixin,
    OrderSwitchWizardView,
    CustomerServiceMixinView,
):
    STEP_NAME = 'cs_order_switch_commit_additional_payment'

    template_name = 'customer_service/order_switch/waiting_for_additional_payments.html'

    def post(self, request, *args, **kwargs):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))

        order.commit_additional_payment(by=self.request.user)
        order.save(update_fields=['switch_status'])
        self.log_cs_order_switch_status_change(order, kwargs)
        messages.success(self.request, 'Project has been changed')
        return redirect(
            reverse_lazy('cs_order_switch_summary_view', kwargs={'order_id': order.id})
        )


class OrderSwitchSummaryView(OrderSwitchWizardView, CustomerServiceMixinView):
    template_name = 'customer_service/order_switch/summary.html'
