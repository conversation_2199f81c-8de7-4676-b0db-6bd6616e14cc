import json

from collections import namedtuple
from decimal import Decimal
from unittest import mock
from unittest.mock import patch

from django.urls import reverse

import pytest

from rest_framework import status

from custom.enums import (
    LanguageEnum,
    ShelfType,
)
from customer_service.tests.factories import CSOrderFactory
from customer_service.views import CustomerServiceMixinView
from events.choices import MailingInvoiceTypeChoices
from events.models import Event
from invoice.choices import InvoiceStatus
from orders.enums import OrderStatus
from orders.switch_status import SwitchStatus
from pricing_v3.services.price_calculators import OrderPriceCalculator
from producers.choices import (
    ProductPriority,
    ProductStatus,
    SourcePriority,
)
from producers.models import ProductPriorityHistory
from regions import constants
from user_profile.choices import UserType
from vouchers.enums import (
    ServiceType,
    VoucherOrigin,
    VoucherType,
)
from vouchers.models import Voucher

request = namedtuple('request', 'user')


@pytest.fixture
def order_with_fixed_price(
    db,
    mocker,
    currency_factory,
    currency_rate_factory,
    region_factory,
    region_rate_factory,
    order_item_factory,
    order_factory,
):
    mocker.patch('gallery.models.Jetty.get_shelf_price_as_number', return_value=125)
    currency = currency_factory(rates=[], code='EUR', name='Euro', symbol='€')
    currency_rate_factory(currency=currency, rate=1.0)
    region = region_factory(currency=currency, is_eu=True)
    region_rate_factory(region=region, rate=1.0)
    order = order_factory(
        items=None,
        country='Germany',
        region=region,
        status=1,
        owner__profile__region=region,
    )
    order_item = order_item_factory(
        region=region,
        order=order,
    )
    order.items.add(order_item)
    OrderPriceCalculator(order).calculate()
    return order


@pytest.fixture
def mock_3rd_party_dependencies(mocker):
    mocker.patch('kpi.kpis.google_analytics_funnel_fetch')
    mocker.patch('invoice.models.Invoice.create_pdf')
    mocker.patch(
        'invoice.models.Invoice._generate_pretty_id_db_sequence',
        return_value='00001/02/2021/1/DE',
    )
    mocker.patch('gallery.models.Jetty.get_weight', return_value=100)
    mocker.patch('gallery.models.Jetty.get_accurate_weight_gross', return_value=110)
    mocker.patch('gallery.models.Jetty.get_shelf_price_as_number', return_value=125)


@pytest.mark.django_db
class TestCustomerServiceDashboardView:
    def test_get_should_return_200_with_proper_context_when_ok(self, admin_client):
        url = reverse('cs_dashboard')
        response = admin_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        expected_list_filter = (CustomerServiceMixinView.USER_TYPE_LOOKUPS,)

        assert expected_list_filter == response.context['list_filter']
        assert 'filters' in response.context


@pytest.mark.django_db
class TestCustomerServiceSearchView:
    def test_get_should_return_200_with_proper_context_when_ok(self, admin_client):
        url = reverse('cs_search')
        response = admin_client.get(url, data={'q_orderid_direct': 123})

        assert response.status_code == status.HTTP_200_OK
        expected_list_filter = (CustomerServiceMixinView.USER_TYPE_LOOKUPS,)

        assert expected_list_filter == response.context['list_filter']
        assert 'filters' in response.context
        assert 'id: 123' == response.context['query']

        assert list(response.context['results']['orders']) == []
        assert list(response.context['results']['userprofiles']) == []
        assert list(response.context['results']['invoices']) == []


@pytest.mark.django_db
class TestRequestCorrectionView:
    def test_get_should_return_200_with_proper_context_when_ok(
        self, invoice_factory, admin_client
    ):

        invoice = invoice_factory(
            status=InvoiceStatus.ENABLED,
            pretty_id='0001/03/2024/183875625/DE',
        )
        url = reverse('cs_request_correction', args=(invoice.id,))
        response = admin_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        expected_list_filter = (CustomerServiceMixinView.USER_TYPE_LOOKUPS,)

        assert expected_list_filter == response.context['list_filter']
        assert 'filters' in response.context
        assert response.context['invoice'] == invoice


@pytest.mark.django_db
class TestRequestCorrectionInvoiceItemView:
    def test_get_should_return_200_with_proper_context_when_ok(
        self, invoice_factory, admin_client
    ):

        invoice = invoice_factory(
            status=InvoiceStatus.ENABLED,
            pretty_id='0001/03/2024/183875625/DE',
        )
        url = reverse('cs_request_correction', args=(invoice.id,))
        response = admin_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        expected_list_filter = (CustomerServiceMixinView.USER_TYPE_LOOKUPS,)

        assert expected_list_filter == response.context['list_filter']
        assert 'filters' in response.context
        assert response.context['invoice'] == invoice


@pytest.mark.django_db
class TestCustomerServiceAddComplaintView:
    @mock.patch(
        'producers.models.Product.get_front_view_svg_from_ps',
        return_value='',
    )
    def test_get_should_return_200_with_proper_context_when_ok(
        self,
        mocked_get_front_view_svg_from_ps,
        manufactor,
        product_factory,
        product_details_jetty_factory,
        admin_client,
    ):
        product = product_factory(
            manufactor=manufactor, cached_physical_product_version=10
        )
        cached_serialization = {
            'item': {
                'elements': [
                    {'elem_type': 'V', 'surname': 'AN', 'production_name': 'C'}
                ]
            }
        }
        product_details_jetty_factory(
            product=product,
            cached_serialization=cached_serialization,
        )
        url = reverse('cs_create_complaint', args=(product.id,))
        response = admin_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        expected_list_filter = (CustomerServiceMixinView.USER_TYPE_LOOKUPS,)

        assert expected_list_filter == response.context['list_filter']
        assert 'filters' in response.context
        assert json.loads(response.context['replaceable_elements_prefixes']) == []
        assert response.context['plywood_thickness'] == 19
        assert not response.context['has_deprecated_drawers']


@pytest.mark.django_db
class TestUserOverviewView:
    def test_get_should_return_200_with_proper_context_when_ok(
        self,
        user,
        admin_client,
    ):
        url = reverse('cs_user_overview', args=(user.id,))
        response = admin_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        expected_list_filter = (CustomerServiceMixinView.USER_TYPE_LOOKUPS,)

        assert expected_list_filter == response.context['list_filter']
        assert 'filters' in response.context
        assert response.context['user'] == user
        assert not response.context['order']
        assert response.context['lat'] is None
        assert response.context['payment_link'] is False
        assert not response.context['orders']
        assert response.context['cart'] is None


@pytest.mark.django_db
class TestUpdateOrderView:
    def test_get_should_return_200_with_proper_context_when_ok(
        self,
        order_factory,
        admin_client,
    ):
        order = order_factory()
        url = reverse('cs_update_order', args=(order.id,))
        response = admin_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        expected_list_filter = (CustomerServiceMixinView.USER_TYPE_LOOKUPS,)

        assert expected_list_filter == response.context['list_filter']
        assert 'filters' in response.context
        assert response.context['order'] == order


@pytest.mark.django_db
class TestCreateDamageFormView:
    def test_get_should_return_200_with_proper_context_when_ok(
        self,
        product,
        admin_client,
    ):

        url = reverse('cs_create_damage_form', args=(product.id,))
        response = admin_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        expected_list_filter = (CustomerServiceMixinView.USER_TYPE_LOOKUPS,)

        assert expected_list_filter == response.context['list_filter']
        assert product.order_id == response.context['order']
        assert 'filters' in response.context


@pytest.mark.django_db
class TestChangeOrderOwnerView:
    def test_get_should_return_200_with_proper_context_when_ok(
        self,
        order,
        admin_client,
    ):

        url = reverse('cs_change_order_owner', args=(order.id,))
        response = admin_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        expected_list_filter = (CustomerServiceMixinView.USER_TYPE_LOOKUPS,)

        assert expected_list_filter == response.context['list_filter']
        assert 'filters' in response.context
        assert order == response.context['order']


@pytest.mark.django_db
class TestOrderSwitchViews:
    def test_cs_order_switch_item_on_hold_should_commit_item_on_hold_and_redirect_to_cs_order_switch_item_replacement_when_post(
        self,
        mocker,
        client,
        admin_user,
        order_item,
        product_factory,
    ):
        mocker.patch(
            'orders.models.OrderSwitchStatusTransitionsMixin.commit_item_on_hold'
        )
        product_factory(
            order=order_item.order,
            order_item=order_item,
        )

        client.force_login(admin_user)

        url = reverse(
            'cs_order_switch_item_on_hold',
            kwargs={'order_id': order_item.order.id},
        )

        payload = {'source_order_item': order_item.id}

        response = client.post(url, payload)

        redirect_url = reverse(
            'cs_order_switch_item_replacement',
            kwargs={'order_id': order_item.order.id},
        )

        assert response.status_code == 302
        assert response['Location'] == redirect_url

    def test_waiting_for_additional_payment_should_commit_additional_payment_and_redirect_to_cs_order_switch_summary_view_when_post(
        self,
        mocker,
        client,
        admin_user,
        order_item_factory,
    ):

        mocker.patch(
            'orders.models.OrderSwitchStatusTransitionsMixin.commit_additional_payment'
        )

        order_item = order_item_factory(
            order__switch_status=SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value,
        )

        client.force_login(admin_user)
        url = reverse(
            'cs_order_switch_commit_additional_payment',
            kwargs={'order_id': order_item.order.id},
        )
        response = client.post(url, {'submit_type': 'Exceptional Release'})

        redirect_url = reverse(
            'cs_order_switch_summary_view',
            kwargs={'order_id': order_item.order.id},
        )

        assert response.status_code == 302
        assert response['Location'] == redirect_url


@pytest.mark.django_db
class TestSendInvoiceView:
    def test_send_invoice_again_by_cs(
        self,
        invoice_factory,
        client,
        admin_user,
    ):
        invoice = invoice_factory(pretty_id='123')
        url = reverse('cs_sent_invoice_again', kwargs={'pk': invoice.id})

        client.force_login(admin_user)
        client.get(url)

        events = Event.objects.filter(event_name='InvoiceReadyEvent')
        event = events.last()

        assert events.count() == 1
        assert event.properties['order_id'] == invoice.order.id
        assert event.properties['email'] == invoice.order.email
        assert event.properties['invoice_url'] == invoice.pdf.url
        assert event.properties['invoice_type'] == MailingInvoiceTypeChoices.NORMAL


@pytest.mark.django_db
class TestChangeUserLang:
    def test_emit_language_update_event(self, client, admin_user, user):
        url = reverse('change_user_lang', kwargs={'user_id': user.id})

        client.force_login(admin_user)
        client.post(url, data={'user_id': user.id, 'language': LanguageEnum.DE})

        events = Event.objects.filter(event_name='LanguageUpdateEvent')
        event = events.last()
        assert events.count() == 1
        assert event.properties['language'] == LanguageEnum.DE


@pytest.mark.django_db
class TestSendOrderNotificationsFormView:
    @pytest.fixture
    def order_in_production(self, order_factory):
        return order_factory(status=OrderStatus.IN_PRODUCTION, email='<EMAIL>')

    @pytest.mark.parametrize(
        ('payment_method', 'is_klarna'),
        [
            ('card', False),
            ('klarna', True),
        ],
    )
    def test_emit_event_when_invoice_notification(
        self,
        client,
        admin_user,
        order_in_production,
        invoice_factory,
        payment_method,
        is_klarna,
    ):
        order_in_production.chosen_payment_method = payment_method
        order_in_production.save(update_fields=['chosen_payment_method'])

        invoice = invoice_factory(pretty_id='123', order=order_in_production)

        url = reverse(
            'cs_send_order_notifications', kwargs={'pk': order_in_production.id}
        )
        client.force_login(admin_user)
        client.post(
            url,
            data={'email_address': order_in_production.email, 'invoice_email': True},
        )

        events = Event.objects.filter(event_name='InvoiceReadyEvent')
        event = events.last()

        assert events.count() == 1
        assert event.properties['order_id'] == order_in_production.id
        assert event.properties['email'] == order_in_production.email
        assert event.properties['invoice_url'] == invoice.pdf.url
        assert event.properties['invoice_type'] == MailingInvoiceTypeChoices.NORMAL

        assert event.properties['is_klarna'] == is_klarna

    @patch(
        'user_profile.models.LoginAccessToken.get_or_create_for_user',
        return_value='abc',
    )
    def test_emit_event_when_order_confirmation_notification(
        self,
        _,
        client,
        admin_user,
        order_in_production,
    ):
        url = reverse(
            'cs_send_order_notifications',
            kwargs={'pk': order_in_production.id},
        )
        client.force_login(admin_user)
        client.post(
            url,
            data={
                'email_address': order_in_production.email,
                'order_confirmation': True,
            },
        )

        events = Event.objects.filter(event_name='OrderSummaryRequestEvent')
        event = events.last()

        assert events.count() == 1
        assert event.properties['order_id'] == order_in_production.id
        assert event.properties['email'] == order_in_production.email
        assert event.properties['is_sample_order'] == (
            order_in_production.contains_only_samples
        )
        assert event.properties['lat'] == 'abc'

    def test_emit_event_when_payment_confirmation(
        self,
        client,
        admin_user,
        order_in_production,
    ):
        url = reverse(
            'cs_send_order_notifications',
            kwargs={'pk': order_in_production.id},
        )
        client.force_login(admin_user)
        client.post(
            url,
            data={
                'email_address': order_in_production.email,
                'payment_confirmation': True,
            },
        )

        events = Event.objects.filter(
            event_name='ResendPaymentConfirmationRequestEvent',
        )
        event = events.last()

        assert events.count() == 1
        assert event.properties['order_id'] == order_in_production.id
        assert event.properties['email'] == order_in_production.email
        assert event.properties['is_sample_order'] == (
            order_in_production.contains_only_samples
        )


@pytest.mark.nbp
@pytest.mark.google
class TestCSOrderPanel:
    @pytest.mark.usefixtures('mock_3rd_party_dependencies')
    def test_order_panel_available(
        self,
        order_with_fixed_price,
        admin_client,
    ):
        CSOrderFactory.create(
            id=order_with_fixed_price.id,
            owner_username=order_with_fixed_price.owner.username,
            status=order_with_fixed_price.status,
        )
        result = admin_client.get(
            f'{reverse("cs_search")}?q_orderid_direct={order_with_fixed_price.id}',
            follow=True,
        )
        assert result.status_code == status.HTTP_200_OK
        assert '/cs/user_overview/' in result.rendered_content

        order_panel = admin_client.get(
            reverse('cs_user_overview', args=[order_with_fixed_price.owner.id]),
            follow=True,
        )
        assert order_panel.status_code == status.HTTP_200_OK
        assert str(order_with_fixed_price.id) in order_panel.rendered_content


@pytest.mark.django_db
class TestChangeProductPriorityView:
    def test_get_should_not_update_priority_when_status_new_and_not_existing_priority(
        self,
        product_factory,
        region_factory,
        currency_factory,
        admin_client,
    ):
        default_currency = currency_factory(**constants.DEFAULT_CURRENCY)
        region = region_factory(currency=default_currency)
        product = product_factory(
            status=ProductStatus.NEW,
            order__region=region,
            order__owner__profile__region=region,
        )
        change_product_priority_url = reverse(
            'change_product_priority', args=(product.id, 0)
        )
        response = admin_client.post(change_product_priority_url, follow=True)

        redirect_url, redirect_status_code = response.redirect_chain[-1]
        assert redirect_url == reverse(
            'cs_user_overview',
            args=[product.order.owner_id],
        )
        assert redirect_status_code == status.HTTP_302_FOUND
        assert response.status_code == status.HTTP_200_OK
        assert 'Failed during changing priority' in [
            msg.message for msg in response.context['messages']
        ]

    @mock.patch('producers.internal_api.events.ProductRefreshEvent.execute')
    def test_get_should_update_priority_with_history_when_status_new_and_correct_priority(
        self,
        mocked_product_refresh_event_execute,
        product_factory,
        region_factory,
        currency_factory,
        admin_client,
    ):
        default_currency = currency_factory(**constants.DEFAULT_CURRENCY)
        region = region_factory(currency=default_currency)
        product = product_factory(
            status=ProductStatus.NEW,
            priority=ProductPriority.NORMAL,
            order__region=region,
            order__owner__profile__region=region,
        )
        change_product_priority_url = reverse(
            'change_product_priority',
            args=(product.id, ProductPriority.ON_HOLD),
        )

        response = admin_client.post(change_product_priority_url, follow=True)

        redirect_url, redirect_status_code = response.redirect_chain[-1]
        assert redirect_url == reverse(
            'cs_user_overview',
            args=[product.order.owner_id],
        )
        assert redirect_status_code == status.HTTP_302_FOUND
        assert response.status_code == status.HTTP_200_OK

        product.refresh_from_db()

        assert product.priority == ProductPriority.ON_HOLD
        priority_history = ProductPriorityHistory.objects.get(product=product)

        assert priority_history.priority == ProductPriority.ON_HOLD
        assert priority_history.previous_priority == ProductPriority.NORMAL

    def test_post_should_not_update_priority_when_the_same(
        self, product_factory, admin_client
    ):
        product = product_factory(
            status=ProductStatus.NEW,
            priority=ProductPriority.NORMAL,
        )
        change_product_priority_url = reverse(
            'change_product_priority', args=(product.pk, ProductPriority.NORMAL)
        )

        response = admin_client.post(change_product_priority_url, follow=True)

        product.refresh_from_db()
        assert not product.product_priority_history.exists()
        assert 'Failed during changing priority' in str(response.content)


@pytest.mark.django_db
class TestChangeSourcePriorityView:
    def test_post_should_not_update_source_priority_when_the_same(
        self, product_factory, admin_client
    ):
        product = product_factory(
            status=ProductStatus.NEW,
            source_priority=SourcePriority.VIP,
        )
        change_product_source_priority_url = reverse(
            'change_source_priority', args=(product.pk, SourcePriority.VIP)
        )

        response = admin_client.post(change_product_source_priority_url, follow=True)

        assert 'Failed during changing priority' in str(response.content)


@pytest.mark.django_db
@pytest.mark.nbp
@mock.patch(
    'customer_service.views.GetTransportCostBaseOnValueAndWeightAPIClient.get_cost',
    return_value=Decimal('22.27'),
)
def test_get_cost_with_transport(mocked_get_cost, product, admin_client):
    weight = 50
    cost = 100
    url = (
        reverse('get_cost_with_transport', kwargs={'product_id': product.id})
        + f'?weight={weight}&cost={cost}'
    )
    response = admin_client.get(url)
    data = response.json()
    assert data['total_cost'] == '27.23'
    assert data['transport_cost'] == '4.96'


@pytest.mark.django_db
def test_deactivate_user_account(admin_client, user_factory):
    base_email = '<EMAIL>'
    user = user_factory(
        email=base_email,
        profile__user_type=UserType.CUSTOMER,
        profile__email=base_email,
        profile__invoice_email=base_email,
        is_active=True,
    )

    url = reverse('deactivate_user_account', kwargs={'pk': user.profile.id})
    response = admin_client.post(url, follow=True)
    assert response.status_code == status.HTTP_200_OK
    user.refresh_from_db()
    expected_email = '<EMAIL>'
    assert user.email == expected_email
    assert user.profile.email == expected_email
    assert user.profile.invoice_email == expected_email
    assert user.is_active is False


@pytest.mark.parametrize(
    'how_many',
    [1, 5],
)
def test_create_promo_code_with_discount(
    how_many,
    admin_client,
):
    value = Decimal('20.0')
    delivery_value = Decimal('15.0')
    data = {
        'kind_of': VoucherType.PERCENTAGE,
        'code': 'test_code',
        'value': value,
        'how_many': how_many,
        'origin': VoucherOrigin.B2B,
        'email': '<EMAIL>',
        'delivery_discount': delivery_value,
        'characters': 10,
        'uses': 5,
        'limit_lower': 10,
        'limit_upper': 20,
    }
    url = reverse('cs_create_promocode')
    response = admin_client.post(url, data=data)
    assert response.status_code == 200
    voucher = Voucher.objects.last()
    discount = voucher.discounts.filter(service_type=ServiceType.DELIVERY).last()
    assert discount.value == delivery_value
    assert voucher.value == value


@pytest.mark.parametrize(
    'how_many',
    [1, 5],
)
def test_create_promo_code_exclude_sotty_from_discount(
    how_many,
    admin_client,
):
    value = Decimal('20.0')
    data = {
        'kind_of': VoucherType.PERCENTAGE,
        'code': 'test_code',
        'value': value,
        'how_many': how_many,
        'origin': VoucherOrigin.B2B,
        'email': '<EMAIL>',
        'characters': 10,
        'uses': 5,
        'limit_lower': 10,
        'limit_upper': 20,
        'excluded_furniture': [ShelfType.SOFA_TYPE01.value],
    }
    url = reverse('cs_create_promocode')
    response = admin_client.post(url, data=data)
    assert response.status_code == 200
    voucher = Voucher.objects.last()
    assert (
        str(ShelfType.SOFA_TYPE01.value)
        in voucher.item_conditionals.get('exclude')[0]['shelf_types']
    )
