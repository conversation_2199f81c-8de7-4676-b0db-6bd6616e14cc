from rest_framework import serializers

from producers.choices import ProductPriority
from producers.models import Product


class RequestProductPostponePrioritySerializer(serializers.Serializer):
    requested_postponed_delivery_date = serializers.DateField()


class ProductPostponePrioritySerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = [
            'id',
            'requested_postponed_delivery_date',
            'expected_postponed_release_date',
        ]


class ProductWithPrioritiesSwitchSerializer(serializers.ModelSerializer):
    is_previous_priority_on_hold = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id',
            'is_previous_priority_on_hold',
        ]

    def get_is_previous_priority_on_hold(self, obj: 'Product') -> bool:
        previous_priority = obj.product_priority_history.exclude_first_on_hold().first()
        return bool(
            previous_priority and previous_priority.priority == ProductPriority.ON_HOLD
        )


class ProductPrioritySerializer(serializers.Serializer):
    priority = serializers.ChoiceField(
        required=False,
        default=ProductPriority.NORMAL,
        choices=ProductPriority.choices,
    )
