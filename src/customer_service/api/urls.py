from django.urls import path

from customer_service.api.views import (
    OrderItemAbortRequestRollbackAPIView,
    OrderSwitchRollbackItemOnHoldAPIView,
    OrderSwitchRollbackItemReplacementAPIView,
    OrderSwitchRollbackItemReplacementItemOnHoldAPIView,
    OrderSwitchRollbackPaymentRecalculationsItemReplacementItemOnHoldAPIView,
    OrderSwitchRollbackRecalculationsItemReplacementItemOnHoldAPIView,
    ProductPostponePriorityAPIView,
)

urlpatterns = [
    path(
        'request-product-postpone/<int:pk>',
        ProductPostponePriorityAPIView.as_view(),
        name='request-product-postpone',
    ),
    path(
        'rollback-abort-request/<int:pk>',
        OrderItemAbortRequestRollbackAPIView.as_view(),
        name='rollback-abort-request',
    ),
    path(
        'order_switch/rollback_item_on_hold/<int:order_id>',
        OrderSwitchRollbackItemOnHoldAPIView.as_view(),
        name='cs-order-switch-rollback-item-on-hold',
    ),
    path(
        'order_switch/rollback_item_replacement/<int:order_id>/',
        OrderSwitchRollbackItemReplacementAPIView.as_view(),
        name='cs-order-switch-rollback-item-replacement',
    ),
    path(
        'order_switch/rollback_item_replacement_and_hold/<int:order_id>',
        OrderSwitchRollbackItemReplacementItemOnHoldAPIView.as_view(),
        name='cs-order-switch-rollback-item-replacement-hold',
    ),
    path(
        'order_switch/rollback_recalculations_item_replacement_hold/<int:order_id>',  # noqa: E501
        OrderSwitchRollbackRecalculationsItemReplacementItemOnHoldAPIView.as_view(),
        name='cs-order-switch-rollback-recalculations-item-replacement-hold',
    ),
    path(
        'order_switch/rollback_payment_recalculations_item_replacement_hold/<int:order_id>',  # noqa: E501
        OrderSwitchRollbackPaymentRecalculationsItemReplacementItemOnHoldAPIView.as_view(),  # noqa: E501
        name='cs-order-switch-rollback-payment-recalculations-item-replacement-hold',
    ),
]
