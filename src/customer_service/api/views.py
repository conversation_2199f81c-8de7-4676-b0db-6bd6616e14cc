import logging

from typing import (
    TYPE_CHECKING,
    Any,
)

from django.contrib import messages
from django.db import transaction
from django.urls import reverse

from rest_framework import (
    generics,
    status,
)
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from rest_framework.views import APIView

from customer_service.api.permissions import (
    IsCustomerServiceUser,
    IsSuperUser,
)
from customer_service.api.serializers import (
    ProductPostponePrioritySerializer,
    ProductPrioritySerializer,
    RequestProductPostponePrioritySerializer,
)
from customer_service.models import OrderItemAbortRequest
from orders.models import Order
from producers.choices import ProductPriority
from producers.errors import CannotChangeToPostponePriorityException
from producers.models import Product

if TYPE_CHECKING:
    from rest_framework.request import Request


logger = logging.getLogger('cstm')


class ProductPostponePriorityAPIView(APIView):
    permission_classes = (IsCustomerServiceUser | IsSuperUser,)

    def post(self, request, pk, *args, **kwargs):
        product = get_object_or_404(Product, pk=pk)

        serializer = RequestProductPostponePrioritySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        requested_postponed_delivery_date = serializer.validated_data[
            'requested_postponed_delivery_date'
        ]
        try:
            product.priority_updater.change_to_postponed(
                requested_postponed_delivery_date
            )
        except CannotChangeToPostponePriorityException as e:
            return Response(
                data={'error': e.message}, status=status.HTTP_400_BAD_REQUEST
            )

        data = ProductPostponePrioritySerializer(product).data
        return Response(data=data, status=status.HTTP_200_OK)


class OrderItemAbortRequestRollbackAPIView(generics.UpdateAPIView):
    queryset = OrderItemAbortRequest.objects.all()
    permission_classes = (IsCustomerServiceUser | IsSuperUser,)

    def update(self, request: 'Request', *args: Any, **kwargs: Any) -> 'Response':
        abort_request = self.get_object()
        abort_request.update_status_to_cancelled()
        return Response(
            {'detail': 'Status updated to CANCELLED.'}, status=status.HTTP_200_OK
        )


class OrderSwitchRollbackItemOnHoldAPIView(APIView):
    permission_classes = (IsCustomerServiceUser | IsSuperUser,)

    def post(self, request: 'Request', order_id: int) -> Response:
        order = get_object_or_404(Order, pk=order_id)
        serializer = ProductPrioritySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        product_priority = serializer.validated_data['priority']
        order.rollback_item_on_hold(by=request.user, product_priority=product_priority)
        order.save()
        messages.success(
            request,
            f'Product successfully changed priority back to '
            f'{ProductPriority(product_priority).label_with_normal_displayed}',
        )
        redirect_url = reverse('cs_user_overview', args=[order.owner_id])
        return Response({'redirect_url': redirect_url}, status=status.HTTP_200_OK)


class OrderSwitchRollbackItemReplacementAPIView(APIView):
    permission_classes = (IsCustomerServiceUser | IsSuperUser,)

    def post(self, request: 'Request', order_id: int) -> Response:
        order = get_object_or_404(Order, pk=order_id)
        order.rollback_item_replacement(by=request.user)
        order.save()
        messages.success(
            request, 'Switch successfully changed to Item Replacement step'
        )
        redirect_url = reverse(
            'cs_order_switch_item_replacement',
            kwargs={'order_id': order_id},
        )
        return Response({'redirect_url': redirect_url}, status=status.HTTP_200_OK)


class OrderSwitchRollbackItemReplacementItemOnHoldAPIView(APIView):
    permission_classes = (IsCustomerServiceUser | IsSuperUser,)

    @transaction.atomic
    def post(self, request: 'Request', order_id: int):
        order = get_object_or_404(Order, pk=order_id)
        serializer = ProductPrioritySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        product_priority = serializer.validated_data['priority']

        order.rollback_item_replacement(by=request.user)
        order.rollback_item_on_hold(by=request.user, product_priority=product_priority)
        order.save()

        messages.success(
            request,
            f'Product successfully changed priority back to '
            f'{ProductPriority(product_priority).label_with_normal_displayed}',
        )
        redirect_url = reverse('cs_user_overview', args=[order.owner_id])
        return Response({'redirect_url': redirect_url}, status=status.HTTP_200_OK)


class OrderSwitchRollbackRecalculationsItemReplacementItemOnHoldAPIView(APIView):
    permission_classes = (IsCustomerServiceUser | IsSuperUser,)

    @transaction.atomic
    def post(self, request: 'Request', order_id: int):
        order = get_object_or_404(Order, pk=order_id)
        serializer = ProductPrioritySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        product_priority = serializer.validated_data['priority']

        order.rollback_recalculations(
            options=order.switch_options,
            by=request.user,
            description=order.switch_options,
        )
        order.rollback_item_replacement(by=request.user)
        order.rollback_item_on_hold(by=request.user, product_priority=product_priority)
        order.save()

        messages.success(
            request,
            f'Product successfully changed priority back to '
            f'{ProductPriority(product_priority).label_with_normal_displayed}',
        )
        redirect_url = reverse('cs_user_overview', args=[order.owner_id])
        return Response({'redirect_url': redirect_url}, status=status.HTTP_200_OK)


class OrderSwitchRollbackPaymentRecalculationsItemReplacementItemOnHoldAPIView(APIView):
    permission_classes = (IsCustomerServiceUser | IsSuperUser,)

    @transaction.atomic
    def post(self, request: 'Request', order_id: int):
        order = get_object_or_404(Order, pk=order_id)
        serializer = ProductPrioritySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        product_priority = serializer.validated_data['priority']

        order.rollback_additional_payment(by=request.user)
        order.rollback_recalculations(
            options=order.switch_options,
            by=request.user,
            description=order.switch_options,
        )
        order.rollback_item_replacement(by=request.user)
        order.rollback_item_on_hold(by=request.user, product_priority=product_priority)
        order.save()

        messages.success(
            request,
            f'Product successfully changed priority back to '
            f'{ProductPriority(product_priority).label_with_normal_displayed}',
        )
        redirect_url = reverse('cs_user_overview', args=[order.owner_id])
        return Response({'redirect_url': redirect_url}, status=status.HTTP_200_OK)
