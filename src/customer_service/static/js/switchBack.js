const requestRollback = (action) => {
  const csrfToken = document.querySelector("[name='csrfmiddlewaretoken']")?.value;

  fetch(action, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": csrfToken
    },
    body: "{}"
  })
    .then(response => {
      if (!response.ok) throw new Error("Network response was not ok");
      return response.json();
    })
    .then(data => {
      window.location.href = data.redirect_url;
    })
    .catch(error => {
      alert(`Request failed. Please try again. Error: ${error.message}`);
    });
};

document.addEventListener("DOMContentLoaded", () => {
  const form = document.getElementById("switch-back");

  form.addEventListener("submit", (event) => {
    event.preventDefault();
    requestRollback(form.action);
  });
});
