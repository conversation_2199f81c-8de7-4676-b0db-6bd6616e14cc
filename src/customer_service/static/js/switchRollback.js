const requestRollbackWithPriority = (action, priority) => {
  const csrfToken = document.querySelector("[name='csrfmiddlewaretoken']")?.value;

  fetch(action, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": csrfToken
    },
    body: priority !== undefined ? JSON.stringify({priority}) : "{}"
  })
    .then(response => {
      if (!response.ok) throw new Error("Network response was not ok");
      return response.json();
    })
    .then(data => {
      window.location.href = data.redirect_url;
    })
    .catch(error => {
      alert(`Request failed. Please try again. Error: ${error.message}`);
    });
};

document.addEventListener("DOMContentLoaded", () => {
  const form = document.getElementById("switch-cancel");
  const confirmSubmitBtn = document.getElementById("confirmSubmitBtn");

  form.addEventListener("submit", (event) => {
    event.preventDefault();

    if (!sourceProduct.is_previous_priority_on_hold) {
      requestRollbackWithPriority(form.action,undefined);
      return;
    }

    $("#confirmModal").modal("show");
    confirmSubmitBtn.onclick = null;

    confirmSubmitBtn.onclick = () => {
      const select = document.getElementById("product-priority-select");
      const selectedPriority = select.value;
      $("#confirmModal").modal("hide");
      requestRollbackWithPriority(form.action, selectedPriority);
    };
  });
});
