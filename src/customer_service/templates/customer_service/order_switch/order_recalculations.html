{% extends "customer_service/base.html" %}
{% load humanize %}
{% load crispy_forms_tags static %}

{% block extrascripts %}
    <script type="text/javascript">
        const sourceProduct = {{ serialized_source_product|safe}};
    </script>
    <script src="{% static 'js/preventDoubleClickOnSubmit.js' %}"></script>
    <script src="{% static 'js/switchRollback.js' %}"></script>
    <script src="{% static 'js/switchBack.js' %}"></script>
{% endblock %}

{% block content %}
    {% include "customer_service/confirmation_modal.html" %}

    <div class="row">
        <div class="col-md-12">
            <h2>Switch Product - Status <b>"{{ order.get_switch_status_display }}"</b></h2>
            <p class="bolder">
                <a href="{% url 'cs_user_overview' order.owner.id %}">Order: {{ order.pk }}</a>
                / {{ order.first_name}} {{ order.last_name }}
            </p>
            <p>Assembly: {{ order.assembly|yesno:"Yes,No" }}</p>
            <div>
                <p>Estimated Delivery Time: {{ order.estimated_delivery_time }}</p>
                {% if order.used_promo %}
                    <hr/>
                    <p>Promo: {{ order.promo_text }}: <a href="{% url 'admin:vouchers_voucher_change' order.used_promo.pk %}">{{ order.used_promo }}</a></p>
                    {% with order.region.get_currency.symbol as currency %}
                        <p><b>Regionalized Promo:</b>
                            Netto: {{ order.region_promo_amount_net }} {{ currency }} /
                            Brutto: {{ order.region_promo_amount }} {{ currency }}
                        </p>
                    {% endwith %}
                    <p><b>In Euro:</b>
                        Netto: {{ order.promo_amount_net }} € /
                        Brutto: {{ order.promo_amount }} €
                    </p>
                {% endif %}
                <hr/>
                {% with order.region.get_currency.symbol as currency %}
                    <p>
                        Region Netto: {{ order.region_total_price_net }} {{ currency }}  /
                        Region Brutto: {{ order.region_total_price }} {{ currency }}
                    </p>
                {% endwith %}
                <p>
                    Netto: {{ order.total_price_net }} € /
                    Brutto: {{ order.total_price }} €
                </p>
            </div>
            <div class="col-md-6">
                <h3>Source Order Item</h3>
                <p>Order Item: {{ order.source_order_item.pk }}</p>
                <img class="item__order_item__preview" src="{{ order.source_order_item.order_item.preview | file_url }}">
                <p>Furniture Type: {{ order.source_order_item.get_furniture_type }}({{ order.source_order_item.get_furniture_id }})</p>
                <p>
                    Product: {{ order.source_order_item.product_set.first.pk }} /
                    Shelf Type: {{ order.source_order_item.product_set.first.cached_shelf_type }} /
                    Priority: <b>"{{ order.source_order_item.product_set.first.get_priority_display }}"</b>
                </p>

                <div class="source-pricing">
                    {% with order.source_order_item.region.get_currency.symbol as currency %}
                        <p><b>Regionalized:</b>
                            Netto: {{ order.source_order_item.region_price_net|safe }} {{ currency }} /
                            Brutto: {{ order.source_order_item.region_price|safe }} {{ currency }}
                        </p>
                    {% endwith %}

                    <p><b>In Euro:</b>
                        Netto: {{ order.source_order_item.price_net|safe }} € /
                        Brutto: {{ order.source_order_item.price|safe }} €
                    </p>

                    <p><b>Assembly:</b></p>
                    {% with order.source_order_item.region.get_currency.symbol as currency %}
                        <p><b>Regionalized:</b>
                            Brutto: {{ order.source_order_item.region_assembly_price|safe }} {{ currency }}
                        </p>
                    {% endwith %}
                    <p><b>In Euro:</b>
                        Brutto: {{ order.source_order_item.assembly_price|safe }} €
                    </p>
                </div>
            </div>

            <div class="col-md-6">
                <h3>Target Order Item</h3>
                <p>Order Item: {{ order.target_order_item.pk }}</p>
                <img class="item__order_item__preview" src="{{ order.target_order_item.order_item.preview | file_url }}">
                <p>Furniture Type: {{ order.target_order_item.get_furniture_type }}({{ order.target_order_item.get_furniture_id }})</p>
                <p>
                    Product: {{ order.target_order_item.product_set.first.pk }} /
                    Shelf Type: {{ order.target_order_item.product_set.first.cached_shelf_type }} /
                    Priority: <b>"{{ order.target_order_item.product_set.first.get_priority_display }}"</b>
                </p>
                <div class="target-pricing">
                    {% with order.target_order_item.region.get_currency.symbol as currency %}
                        <p><b>Regionalized:</b>
                            Netto: {{ order.target_order_item.region_price_net|safe }} {{ currency }} /
                            Brutto: {{ order.target_order_item.region_price|safe }} {{ currency }}
                        </p>
                    {% endwith %}

                    <p><b>In Euro:</b>
                        Netto: {{ order.target_order_item.price_net|safe }} € /
                        Brutto: {{ order.target_order_item.price|safe }} €
                    </p>

                    <p><b>Assembly:</b></p>
                    {% with order.target_order_item.region.get_currency.symbol as currency %}
                        <p><b>Regionalized:</b>
                            Brutto: {{ order.target_order_item.region_assembly_price|safe }} {{ currency }}
                        </p>
                    {% endwith %}
                    <p><b>In Euro:</b>
                        Brutto: {{ order.target_order_item.assembly_price|safe }} €
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 total-price-diff">
            <p><b>Price difference (Order Items):</b></p>
            {% with order.target_order_item.region.get_currency.symbol as currency %}
                <p><b>Regionalized:</b>
                    Assembly: {{ order.region_assembly_price_target_minus_source|safe }} {{ currency }} /
                    Netto: {{ order.region_price_target_minus_source.netto|safe }} {{ currency }} /
                    Brutto: {{ order.region_price_target_minus_source.brutto|safe }} {{ currency }}
                </p>
            {% endwith %}
            <p><b>In Euro:</b>
                Netto: {{ order.price_target_minus_source.netto|safe }} € /
                Brutto: {{ order.price_target_minus_source.brutto|safe }} €
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="col-md-6">
                <h3>Source Order</h3>
                <div class="source-pricing">
                    {% with order.source_order_item.region.get_currency.symbol as currency %}
                        <p><b>Regionalized:</b>
                            Netto: {{ order.source_region_total_price_net|safe }} {{ currency }} /
                            Brutto: {{ order.source_region_total_price|safe }} {{ currency }}
                        </p>
                    {% endwith %}
                    <p><b>In Euro:</b>
                        Netto: {{ order.source_total_price_net|safe }} € /
                        Brutto: {{ order.source_total_price|safe }} €
                    </p>
                </div>
            </div>

            <div class="col-md-6">
                <h3>Target Order</h3>
                <div class="target-pricing">
                    {% with order.target_order_item.region.get_currency.symbol as currency %}
                        <p><b>Regionalized:</b>
                            Netto: {{ order.region_total_price_net|safe }} {{ currency }} /
                            Brutto: {{ order.region_total_price|safe }} {{ currency }}
                        </p>
                    {% endwith %}
                    <p><b>In Euro:</b>
                        Netto: {{ order.total_price_net|safe }} € /
                        Brutto: {{ order.total_price|safe }} €
                    </p>
                </div>
            </div>
        </div>
    </div>

    {% include "customer_service/order_switch/_order_diff.html" with class="total-price-diff"  %}

    <div class="row">
        <div class="col-md-12">
            <form action="{% url 'cs_order_switch_order_recalculations' order.pk %}" method="post">
                {% csrf_token %}
                {{ form|crispy }}
                <input type="submit" class="btn btn-info btn-next btn-prevent-doubleclick" value="Recalculate Pricing" />
            </form>

            <form id="switch-cancel" action="{% url 'cs-order-switch-rollback-item-replacement-hold' order.pk %}" method="post">
                {% csrf_token %}
                <input type="submit" class="btn btn-info btn-cancel btn-prevent-doubleclick" value="Cancel" />
            </form>

            <form id="switch-back" action="{% url 'cs-order-switch-rollback-item-replacement' order.pk %}" method="post">
                {% csrf_token %}
                <input type="submit" class="btn btn-info btn-go-back btn-prevent-doubleclick" value="Go back" />
            </form>
        </div>
    </div>
{% endblock %}

