{% load humanize %}

<div>
    <div class="row">
        <div class="col-sm-2">
            <p class="bolder">Order Item: {{ order.source_order_item.pk }}</p>
            <p>
                <a target="_blank" href="{{ order.source_order_item.order_item.get_item_url }}">
                    Gallery Id: {{ order.source_order_item.order_item.id }}
                </a>
            </p>
            <p>
                Furniture Type: {{ order.source_order_item.get_furniture_type }}
            </p>
            <p>
                Created at: {{ order.source_order_item.order_item.created_at|naturalday }}
            </p>
        </div>
        <div class="col-sm-2">
            <p>Product: {{ order.source_order_item.product_set.first.pk }}</p>
            <p>Shelf Type: {{ order.source_order_item.product_set.first.cached_shelf_type }}</p>
            <p>Priority: <b>"{{ order.source_order_item.product_set.first.get_priority_display }}"</b></p>
        </div>
        <div class="col-sm-2">
            {% with order.region.get_currency.symbol as currency %}
                <p class="list-group-item-text"> Regionalized:</p>
                <p class="list-group-item-text"> Netto: {{ order.source_order_item.region_price_net|safe }} {{ currency }}</p>
                <p class="list-group-item-text"> Brutto: {{ order.source_order_item.region_price|safe }} {{ currency }}</p>
                <p class="list-group-item-text"> Promo: {{ order.source_order_item.region_promo_value|safe }} {{ currency }}</p>
            {% endwith %}
            <br/>
            <p class="list-group-item-text"> In Euro:</p>
            <p class="list-group-item-text"> Netto: {{ order.source_order_item.price_net|safe }} €</p>
            <p class="list-group-item-text"> Brutto: {{ order.source_order_item.price|safe }} €</p>
        </div>

        <div class="col-sm-4">
            <img class="item__order_item__preview" src="{{ order.source_order_item.order_item.preview | file_url }}">
        </div>
    </div>

    {% if order.target_order_item and order.is_target_attached_to_order %}
        <div class="row" style="margin-top: 20px;">
            <div class="col-sm-2">
                <p class="bolder">Order Item: {{ order.target_order_item.pk }}</p>

                <p>
                    <a target="_blank" href="{{ order.target_order_item.order_item.get_item_url }}">
                        Gallery Id: {{ order.target_order_item.order_item.id }}
                    </a>
                </p>
                <p>
                    Furniture Type: {{ order.target_order_item.get_furniture_type }}
                </p>
                <p>
                    Created at: {{ order.target_order_item.order_item.created_at|naturalday }}
                </p>
            </div>
            <div class="col-sm-2">
                <p>Product: {{ order.target_order_item.product_set.first.pk }}</p>
                <p>Shelf Type: {{ order.target_order_item.product_set.first.cached_shelf_type }}</p>
                <p>Priority: <b>"{{ order.target_order_item.product_set.first.get_priority_display }}"</b></p>
            </div>
            <div class="col-sm-2">
                {% with order.region.get_currency.symbol as currency %}
                    <p class="list-group-item-text"> Regionalized:</p>
                    <p class="list-group-item-text"> Netto: {{ order.target_order_item.region_price_net|safe }} {{ currency }}</p>
                    <p class="list-group-item-text"> Brutto: {{ order.target_order_item.region_price|safe }} {{ currency }}</p>
                    <p class="list-group-item-text"> Promo: {{ order.target_order_item.region_promo_value|safe }} {{ currency }}</p>
                {% endwith %}
                <br/>
                <p class="list-group-item-text"> In Euro:</p>
                <p class="list-group-item-text"> Netto: {{ order.target_order_item.price_net|safe }} €</p>
                <p class="list-group-item-text"> Brutto: {{ order.target_order_item.price|safe }} €</p>
            </div>

            <div class="col-sm-4">
                <img class="item__order_item__preview" src="{{ order.target_order_item.order_item.preview | file_url }}">
            </div>
        </div>
    {% endif %}
</div>
