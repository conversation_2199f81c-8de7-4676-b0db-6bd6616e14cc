{% extends "customer_service/base.html" %}
{% load humanize static %}

{% block extrascripts %}
    <script type="text/javascript">
        const sourceProduct = {{ serialized_source_product|safe}};
    </script>
    <script src="{% static 'js/preventDoubleClickOnSubmit.js' %}"></script>
    <script src="{% static 'js/switchRollback.js' %}"></script>
{% endblock %}

{% block content %}
    {% include "customer_service/confirmation_modal.html" %}

    <div class="row">
        <div class="col-md-12">
            <h2>Switch Product - Status <b>"{{ order.get_switch_status_display }}"</b></h2>
            <p class="bolder">
                <a href="{% url 'cs_user_overview' order.owner.id %}">Order: {{ order.pk }}</a>
                / {{ order.first_name}} {{ order.last_name }}
            </p>
            <p>Assembly: {{ order.assembly|yesno:"Yes,No" }}</p>
            <div>
                <p>Estimated Delivery Time: {{ order.estimated_delivery_time }}</p>
                {% if order.used_promo %}
                    <hr/>
                    <p>Promo: {{ order.promo_text }}: <a href="{% url 'admin:vouchers_voucher_change' order.used_promo.pk %}">{{ order.used_promo }}</a></p>
                    {% with order.region.get_currency.symbol as currency %}
                        <p><b>Regionalized Promo:</b>
                            Netto: {{ order.region_promo_amount_net }} {{ currency }} /
                            Brutto: {{ order.region_promo_amount }} {{ currency }}
                        </p>
                    {% endwith %}
                    <p><b>In Euro:</b>
                        Netto: {{ order.promo_amount_net }} € /
                        Brutto: {{ order.promo_amount }} €
                    </p>
                {% endif %}
                <hr/>
                {% with order.region.get_currency.symbol as currency %}
                    <p>
                        Region Netto: {{ order.region_total_price_net }} {{ currency }}  /
                        Region Brutto: {{ order.region_total_price }} {{ currency }}
                    </p>
                {% endwith %}
                <p>
                    Netto: {{ order.total_price_net }} € /
                    Brutto: {{ order.total_price }} €
                </p>
            </div>
            <p>You have successfully switched:</p>
            {% include 'customer_service/order_switch/_source_target_diff.html' %}
        </div>

        {% include "customer_service/order_switch/_order_diff.html" %}
        {% if order.is_cancel_switch_allowed %}
            <form id="switch-cancel" action="{% url 'cs-order-switch-rollback-payment-recalculations-item-replacement-hold' order.pk %}" method="post">
                {% csrf_token %}
                <input type="submit" class="btn btn-info btn-cancel btn-prevent-doubleclick" value="Cancel" />
            </form>
        {% else %}
            Switch Cancel not allowed, because CSCorrectionRequest already accepted.
        {% endif %}
    </div>
{% endblock %}
