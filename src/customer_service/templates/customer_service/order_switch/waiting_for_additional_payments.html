{% extends "customer_service/base.html" %}
{% load humanize static %}

{% block extrascripts %}
    <script type="text/javascript">
        const sourceProduct = {{ serialized_source_product|safe }};
    </script>
    <script src="{% static 'js/preventDoubleClickOnSubmit.js' %}"></script>
    <script src="{% static 'js/switchRollback.js' %}"></script>
{% endblock %}

{% block content %}
    {% include "customer_service/confirmation_modal.html" %}

    <div class="row">
        <div class="col-md-10">
            <h2>Switch Product - Status <b>"{{ order.get_switch_status_display }}"</b></h2>
            <p class="bolder">
                <a href="{% url 'cs_user_overview' order.owner.id %}">Order: {{ order.pk }}</a>
                / {{ order.first_name}} {{ order.last_name }}
            </p>

            {% include 'customer_service/order_switch/_source_target_diff.html' %}

            {% include "customer_service/order_switch/_order_diff.html" %}

            <form action="{% url 'cs_order_switch_commit_additional_payment' order.pk %}" method="post">
                {% csrf_token %}
                <input type="submit" class="btn btn-customer-paid btn-info btn-confirmation" value="Customer Paid"/>
            </form>
            {% if order.is_cancel_switch_allowed %}
                <form id="switch-cancel" action="{% url 'cs-order-switch-rollback-recalculations-item-replacement-hold' order.pk %}" method="post">
                    {% csrf_token %}
                    <input type="submit" class="btn btn-info btn-cancel btn-prevent-doubleclick" value="Cancel" />
                </form>
            {% else %}
                Switch Cancel not allowed, because CSCorrectionRequest already accepted.
            {% endif %}
        </div>
    </div>
{% endblock %}
