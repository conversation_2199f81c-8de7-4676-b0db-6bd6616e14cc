{% load humanize %}
{% load admin_tags %}
{% load cs_tags %}
{% load i18n %}
{% load static %}

<div class="panel panel-default">{# panel start #}
    <div class="panel-heading" role="tab" id="heading{{ order.id }}"> {# panel head start #}
        <h4 class="panel-title">
            <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapse{{ order.id }}" aria-expanded="true" aria-controls="collapse{{ order.id }}">
                Summary for order: {{ order.id }} {% if order.assembly %}- with assembly{% endif %} {% if order.order_type == 6 %}<span class="status_color_6">Reproduction for order: {{ order.parent_order_id }}</span> {% endif %}
                Status: <span class="status_color_{{ order.status }}">{{ order.get_status_display }}</span>{% if is_connected_to_active_cart %}<span class="active_cart_color"> - Connected to active cart </span>{% endif %}
                {% if order.is_influ_order %}<span class="status_color_0">Influencer</span>{% endif %}
                {% if order.is_vip_order %}<span class="status_color_0">VIP</span>{% endif %}

                {% if order.is_switch_status_in_progress %}
                    Switch Status: <span class="status_color_{{ order.switch_status }}">{{ order.get_switch_status_display }}</span>
                {% endif %}
                {% if order.is_klarna_b2b_payment %}
                    <span class="status_color_6">Billie payment method</span>
                {% elif order.is_klarna_payment %}
                    <span class="status_color_6">Klarna Payment Method</span>
                {% endif %}
            </a>
        </h4>
    </div> {# panel head end #}
<div id="collapse{{ order.id }}" class="panel-collapse collapse" role="tabpanel" aria-labelledby="heading{{ order.id }}">{# panel body start #}
<div class="panel-body">
    <div class="row">
        <div class="col-sm-6 bootcards-cards">
            <!--contact details -->
            <div id="contactCard">
                <div class="panel panel-default">
                    <div class="list-group">
                        <div class="list-group-item">
                            <label>(JETTY/WATTY) Warranty Status:</label>
                            <h4 class="list-group-item-heading">
                                <p class="list-group-item-text">
                                {% with warranty_status=order.get_warranty_expiration %}
                                    {% if warranty_status %}
                                        {% now "Y-m-d" as todays_date %}
                                        {% if warranty_status|date:"Y-m-d" > todays_date %}
                                            <span class="btn btn-success">Warranty Active (expires: {{ warranty_status|date:"d.m.Y" }})</span>
                                        {% else %}
                                            <span class="btn btn-danger">Warranty Expired (expired: {{ warranty_status|date:"d.m.Y" }})</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="btn btn-warning">No warranty information available</span>
                                    {% endif %}
                                {% endwith %}
                                </p>
                            </h4>
                            <label>Admin link</label>
                            <h4 class="list-group-item-heading">
                                <p class="list-group-item-text">
                                    <a href="{% url 'admin:orders_order_change' order.id %}"  class="btn btn-info">
                                        {{ order.id }}
                                    </a>
                                </p>
                            </h4>
                            <label>Order status</label>
                            <h4 class="list-group-item-heading">
                                <p class="list-group-item-text">
                                    <span class="btn btn-success">{{ order.get_status_display }}</span>
                                    <a href="{% url 'cs_change_order_status' order.id %}" class="btn btn-info">
                                        Change order status
                                    </a>
                                </p>
                            </h4>
                            <label>Assembly Status:</label>
                            <h4 class="list-group-item-heading">
                                <p class="list-group-item-text">
                                    <span class="btn btn-success">{{ order.get_assembly_status }}</span>
                                </p>
                            </h4>
                            <label>And product statuses:</label>
                            {% for product in order.product_set.all %}
                                <h4 class="list-group-item-heading">
                                    <p class="list-group-item-text">
                                        <span class="btn btn-success">{{ product.get_status_display }}</span>
                                    </p>
                                </h4>
                            {% endfor %}
                        <p>
                            <a href="{% url 'cs_stop_mailing_flow' order.id %}"
                               {% if order.last_logistic_order.mailing_disabled %}disabled='disabled'{% endif %}
                               class="btn btn-info">
                                Stop product delivered flows
                            </a>
                        </p>
                        {% if order.get_free_return_status is not None  %}
                            <p>
                                <a href="#free_return_{{ order.id }}" class="btn" style="border-color: #ddd;">
                                    Free return status: {{ order.get_free_return_status }}
                                </a>
                            </p>
                        {% endif %}
                        <label>Returning client</label>
                            <h4><p>{{ order.returning_client|yesno:"Yes,No" }}</p></h4>
                            <label>Payment link</label>
                            <h4 class="list-group-item-heading">
                                <p class="list-group-item-text">
                                    </span>{% if order.status == 0 or order.status == 1 or order.status == 2 or order.status == 6 or order.status == 9 %}
                                    {% if payment_link == True and order.id == lat_order.id %}
                                        https://{{ current_site }}{% url_with_region_and_language 'front-payment-method' order.owner.profile.language order.region.country.code order.id %}?lat={{ lat }}
                                    {% elif payment_link == False %}
                                        <a href="{% url 'cs_user_overview' order.owner.id %}?payment_link=True&order_id={{order.id}}" class="btn btn-info">Generate payment link</a>
                                    {% endif %}
                                {% else %}
                                    Order should be in different status
                                {% endif %}
                                </p>
                            </h4>
                        </div>

                        {% if cs == True %}
                        <div class="list-group-item">
                            <label>User</label>
                            <h4 class="list-group-item-heading">{{ order.owner }}
                                <a type="button" class="btn btn-info" href="{% url 'cs_change_order_owner' order.id %}">
                                    Change User
                                </a>
                            </h4>
                        </div>
                        {% endif  %}
                        <div class="list-group-item">
                            <label>Name</label>
                            {% if cs == True %}
                                <h4 class="list-group-item-heading">{{ order.get_customer_as_string|safe }}</h4>
                            {% else %}
                                <h4 class="list-group-item-heading">
                                    <a href="/admin/user_profile/userprofile/{{ order.owner.profile.id }}/">
                                    {{ order.get_customer_as_string|safe }}
                                    </a>
                                </h4>
                            {% endif %}
                        </div>
                        <div class="list-group-item">
                            <label>Email</label>
                            {% if cs == True %}
                                <h4 class="list-group-item-heading">{{ order.email }} / {{ order.invoice_email }} </h4>
                            {% else %}
                                <h4 class="list-group-item-heading">{{ order.email }} / {{ order.invoice_email }}</h4>
                            {% endif %}
                        </div>
                        <div class="list-group-item">
                            <label>Order type</label>
                            <h4 class="list-group-item-heading">{{ order.get_order_type_display }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Phone numbers</label>
                            <h4 class="list-group-item-heading">
                                Delivery: {{ order.phone }} <br/>Invoice: {{ order.invoice_phone|default:"" }}
                            </h4>
                        </div>
                        <div class="list-group-item">
                            <label>Country</label>
                            <h4 class="list-group-item-heading">{{ order.get_country }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Company and vat</label>
                            <h4 class="list-group-item-heading">
                                {{ order.company_name|default:"Not set" }}<br/>
                                {{ order.vat|default:"" }}
                            </h4>
                        </div>
                        <div class="list-group-item">
                            <label>Order delivery address</label>
                            <h4 class="list-group-item-heading">
                                {{ order.first_name|default:"" }} {{ order.last_name|default:"" }}<br/>
                                {{ order.street_address_1|default:"" }}<br/>
                                {{ order.street_address_2|default:"" }}<br/>
                                {{ order.postal_code|default:""}} {{ order.city }}<br/>
                                {{ order.country|default:"Not set" }}<br/>
                            </h4>
                        </div>
                        <div class="list-group-item">
                            <label>Invoice Company and vat</label>
                            <h4 class="list-group-item-heading">
                                {{ order.invoice_company_name|default:"Not set" }}<br/>
                                {{ order.invoice_vat|default:"" }}
                            </h4>
                        </div>
                        <div class="list-group-item">
                            <label>Invoice address</label>
                            <h4 class="list-group-item-heading">
                                {{ order.invoice_first_name|default:"" }} {{ order.invoice_last_name|default:"" }}
                                {{ order.invoice_street_address_1|default:"" }}<br/>
                                {{ order.invoice_street_address_2|default:"" }}<br/>
                                {{ order.invoice_postal_code|default:""}} {{ order.invoice_city }}<br/>
                                {{ order.invoice_country|default:"Not set" }}<br/>
                            </h4>
                        </div>

                        <div class="list-group-item">
                            <label>Total value</label>
                            <h4 class="list-group-item-heading">{{ order.get_total_price }}</h4>
                        </div>

                        <div class="list-group-item">
                            <label>Discount amount</label>
                            {% if order.promo_amount > 0 %}
                                <h4 class="list-group-item-heading">
                                    Code: {{ order.promo_text|default_if_none:"" }}, for {{ order.get_promo_amount }}
                                </h4>
                            {% else %}
                                <h4 class="list-group-item-heading">0</h4>
                            {% endif %}
                        </div>

                        <div class="list-group-item">
                            <label>Created at</label>
                            <h4 class="list-group-item-heading">{{ order.created_at }}</h4>
                        </div>

                        <div class="list-group-item">
                            <label>Paid at</label>
                            <h4 class="list-group-item-heading">{{ order.paid_at }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Chosen payment type (on first try)</label>
                            <h4 class="list-group-item-heading">{{ order.chosen_payment_method }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>All payment actions summary</label>
                            <table style="width: 100%;">
                                <tbody>
                                <tr>
                                    <th rowspan="1" colspan="1"><p><strong>Date</strong></p></th>
                                    <th rowspan="1" colspan="1"><p><strong>Payment type</strong></p></th>
                                    <th rowspan="1" colspan="1"><p><strong>Refusal reason or SUCCESS</strong></p></th>
                                </tr>
                                {% for transaction_description in order.transaction_description %}
                                    <tr>
                                        <td rowspan="1" colspan="1"><p>{{ transaction_description.date }}</p></td>
                                        <td rowspan="1" colspan="1">
                                            <p>{{ transaction_description.payment_method }}</p>
                                        </td>
                                        <td rowspan="1" colspan="1">
                                        {% if transaction_description.success %}
                                            <p class="status_color_3">PAID</p>
                                        {% else %}
                                            <p>{{ transaction_description.refusal_reason }}</p>
                                        {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <div class="list-group-item">
                            <label>Settled at</label>
                            <h4 class="list-group-item-heading">{{ order.settled_at|default:"not yet" }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Payable booking date  at</label>
                            <h4 class="list-group-item-heading">{{ order.payable_booking_date|default:"not yet" }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Planned production end (after purchase)</label>
                            <h4 class="list-group-item-heading">{{ order.originally_estimated_delivery_time |default:"not yet" }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Calculated production range (for current estimated delivery date):</label>
                            <h4 class="list-group-item-heading">{{ order.production_range_formatted }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Calculated delivery range (for current estimated delivery date):</label>
                            <h4 class="list-group-item-heading">{{ order.delivery_range_formatted }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Planned production end by product</label>
                            <table style="width: 100%;">
                                <tbody>
                                <tr>
                                    <th rowspan="1" colspan="1"><p><strong>Shelf ID</strong></p></th>
                                    <th rowspan="1" colspan="1"><p><strong>production end</strong></p></th>
                                    <th rowspan="1" colspan="1"><p><strong>in time?</strong></p></th>
                                    <th rowspan="1" colspan="1"><p><strong>production time</strong></p></th>
                                </tr>
                                {% for product in order.product_set.all %}
                                    {% if product.batch %}
                                        <tr>
                                            <td rowspan="1" colspan="1"><p>{{ product.pk }}</p></td>
                                            <td rowspan="1" colspan="1">
                                                <p>{{ product.order.estimated_delivery_time }}</p>
                                            </td>
                                            <td rowspan="1" colspan="1" style="background-color:
                                                {% if not product.order.estimated_delivery_time %}
                                                    dodgerblue;
                                                {% elif product.order.is_production_delayed %}
                                                    red
                                                {% else %}
                                                    greenyellow
                                                {% endif %};">
                                            {% if product.order.estimated_delivery_time %}
                                                <p>{{ product.order.production_delayed_weeks }}</p>
                                            {% else %}
                                                <p>no order estimated delivery date </p>
                                            {% endif %}
                                            </td>
                                            <td rowspan="1" colspan="1">
                                                <p>{{ product.order.production_time_weeks }}</p>
                                            </td>
                                        </tr>
                                    {% endif %}
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="list-group-item">
                            <label>Domestic Injection Mail</label>
                            {% if domestic_injection_mail_sent_at %}
                                <table class="email24-table" style="width: 100%">
                                <tbody>
                                    <tr>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>Carrier</strong></p></th>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>Mail sent at</strong></p></th>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>Days</strong></p></th>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>Info</strong></p></th>
                                    </tr>
                                    <tr>
                                        <td class="email24-table" rowspan="1" colspan="1">
                                            <p>{{ carrier_name }}</p>
                                        </td>
                                        <td class="email24-table" rowspan="1" colspan="1">
                                            <p>{{ domestic_injection_mail_sent_at }}</p>
                                        </td>
                                        <td class="email24-table" rowspan="1" colspan="1">
                                            <p>{{ domestic_injection_mail_days }}</p>
                                        </td>
                                        <td class="email24-table" rowspan="1" colspan="1">
                                            <p>The carrier should contact the client regarding the delivery by {{domestic_injection_mail_contact_until_date}}</p>
                                        </td>
                                    </tr>
                                </tbody>
                                </table>
                            {% else %}
                                <h4 class="list-group-item-heading">No related Domestic Injection Mail</h4>
                            {% endif %}
                        </div>
                        <div class="list-group-item">
                            <label>Email24</label>
                            {% if email24 %}
                                <table class="email24-table" style="width: 100%">
                                <tbody>
                                    <tr>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>ID</strong></p></th>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>Status</strong></p></th>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>Date sent</strong></p></th>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>Email</strong></p></th>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>URL</strong></p></th>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>Details</strong></p></th>
                                        <th class="email24-table-header" rowspan="1" colspan="1"><p><strong>Carrier</strong></p></th>
                                    </tr>
                                    <tr>
                                        <td class="email24-table" rowspan="1" colspan="1">
                                            <a href="{{ email24.admin_url }}">{{ email24.id }}</a>
                                        </td>
                                        <td class="email24-table" rowspan="1" colspan="1">
                                            <p>{{ email24.status }}</p>
                                        </td>
                                        <td class="email24-table" rowspan="1" colspan="1">
                                            <p>{{ email24.date_sent }}</p>
                                        </td>
                                        <td class="email24-table"
                                            style="text-align: center"
                                            rowspan="1" colspan="1"
                                        >
                                            <p>{{ email24.email }}</p>
                                        </td>
                                        <td class="email24-table" rowspan="1" colspan="1">
                                            <p>{{ email24.url }}</p>
                                        </td>
                                        <td class="email24-table" rowspan="1" colspan="1">
                                            {% if email24.details %}
                                                {% for key, value in email24.details.items %}
                                                    <p><b>{{ key }}:</b> {{ value }}</p>
                                                {% endfor %}
                                            {% else %}
                                                <p>Email24 was not confirmed</p>
                                            {% endif %}
                                        </td>
                                        <td class="email24-table" rowspan="1" colspan="1">
                                            <p><b>{{ carrier_name }}</b><br>The carrier will schedule the delivery 3 to 5 days after the order has been dispatched.</p>
                                        </td>
                                    </tr>
                                </tbody>
                                </table>
                            {% else %}
                                <h4 class="list-group-item-heading">No related email24</h4>
                            {% endif %}
                        </div>
                        {% if dtfs %}
                            <div class="list-group-item">
                                <label>Direct Transport Proposition</label>
                                <table style="width: 100%;">
                                    <tbody>
                                    <tr>
                                        <th rowspan="1" colspan="1"><p><strong>ID</strong></p></th>
                                        <th rowspan="1" colspan="1"><p><strong>status</strong></p></th>
                                        <th rowspan="1" colspan="1"><p><strong>note</strong></p></th>
                                        <th rowspan="1" colspan="1"><p><strong>proposal dates</strong></p></th>
                                        <th rowspan="1" colspan="1"><p><strong>selected by client</strong></p></th>
                                        <th rowspan="1" colspan="1"><p><strong>accepted</strong></p></th>
                                        <th rowspan="1" colspan="1"><p><strong>link</strong></p></th>
                                        <th rowspan="1" colspan="1"><p><strong>Carrier</strong></p></th>
                                    </tr>
                                    {% for dtf in dtfs %}
                                        <tr>
                                            <td rowspan="1" colspan="1"><p>{{ dtf.id }}</p></td>
                                            <td rowspan="1" colspan="1">
                                            <p>{{ dtf.get_status_display }}</p></td>
                                            <td rowspan="1" colspan="1">
                                            <p>{{ dtf.note }}</p></td>
                                            <td>
                                                {% for dtf_date in dtf.proposal_dates %}
                                                    <p>{{ dtf_date.date }}</p>
                                                {% endfor %}
                                            </td>
                                            <td>
                                                {% for dtf_date in dtf.selected_dates %}
                                                    <p style="background-color:yellow">
                                                        {{ dtf_date.date }}
                                                        {% if dtf_date.all_day %}
                                                            all day
                                                        {% else %}
                                                            {{ dtf_date.start_hour|time:"H:i" }}-{{ dtf_date.end_hour|time:"H:i" }}
                                                        {% endif %}
                                                    </p>
                                                {% endfor %}
                                            </td>
                                            <td>
                                                <p>
                                                    {% if dtf.accepted_date %}
                                                        <p style="background-color:greenyellow">
                                                            {{ dtf.accepted_date.date }}
                                                            {% if dtf.accepted_date.all_day %}
                                                                all day
                                                            {% else %}
                                                                {{ dtf.accepted_date.start_hour|time:"H:i" }}-{{ dtf.accepted_date.end_hour|time:"H:i" }}
                                                            {% endif %}
                                                        </p>
                                                    {% endif %}
                                                </p>
                                            </td>
                                            <td>
                                                <a href="{% url 'dtf-proposal' dtf.id %}">DTF LINK</a>
                                            </td>
                                            <td>
                                                <p><b>{{ carrier_name }}</b><br>The carrier will schedule the delivery 3 to 5 days after the order has been dispatched.</p>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% endif %}
                        {% for assembly_service in assembly_services_with_dates %}
                            <div class="list-group-item">
                                <label>
                                    Assembly service {{ assembly_service.service_status_description }} for logistic_order {{ assembly_service.logistic_order }}
                                </label>
                                {% if assembly_service.service_dates or assembly_service.status == service_statuses.IN_PROGRESS %}
                                    <table class="table table-bordered">
                                        <tbody>
                                            <tr class="{{ assembly_service.service_info.color_class }}">
                                                <td colspan="3">{{ assembly_service.service_info.description }}</td>
                                            </tr>
                                        {% for service_date in assembly_service.service_dates %}
                                            <tr
                                                {% if service_date.status == service_date_proposal_statuses.ACCEPTED %} class="as_accepted"
                                                {% elif service_date.status == service_date_proposal_statuses.REJECTED%} class="as_rejected"
                                                {% else %} class="{{ assembly_service.service_info.color_class }}"
                                                {% endif %}
                                            >
                                                <td>Set date: {{ service_date.date|date:"d.m:Y"}}</td>
                                                <td>Set time: {{ service_date.from_hour|time:"H:i" }}-{{ service_date.to_hour|time:"H:i" }}</td>
                                                <td>{{ service_date.get_status_display }}</td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                {% endif %}
                            </div>
                        {% endfor %}
                        <div class="list-group-item">
                            <label>Delivered at</label>
                            <h4 class="list-group-item-heading">{{ order.get_delivery_date |default:"not yet"}}</h4>
                        </div>

                    {% if cs == False %}
                        <div class="list-group-item">
                            <label>Link to filtered lists (for actions)</label>
                            <h4 class="list-group-item-heading">
                                <a href="{% url "admin:orders_order_changelist" %}?id={{ order.id }}">Orders</a>
                            </h4>
                            <h4 class="list-group-item-heading">
                                <a href="{% url "admin:invoice_invoice_changelist" %}?order_id={{ order.id }}">Invoices</a>
                            </h4>
                            {% if order.earliest_invoice_domestic_version_supported %}
                                <h4 class="list-group-item-heading">
                                    <a href="{% url "admin:invoice_invoicedomestic_changelist" %}?order_id={{ order.id }}">Domestic Invoices</a>
                                </h4>
                            {% endif %}
                        </div>
                    {% endif %}
                    </div>
                    {% if cs == False %}
                    <div class="panel-footer">
                        <a class="btn btn-link btn-xs pull-right" href="/admin/orders/order/{{ order.id }}/">
                            <small class="pull-left">Go to order edit/profile view</small>
                        </a>
                    </div>
                {% endif %}
                </div>
            </div><!--contact card-->
        </div><!--list-details-->

        <div class="col-sm-6 bootcards-cards">
        {% if cs %}
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading clearfix order_notes_header">
                            <h3 class="panel-title pull-left">Order notes</h3>
                            <h6 class="panel-title pull-right">(double click to edit)</h6>
                            <h6 class="panel-title pull-right hidden order_notes_header_loader" id="order_notes_header_loader_{{ order.id }}">
                                <svg style="margin-top: -10px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2400 2400" width="24" height="24">
                                    <g stroke-width="200" stroke-linecap="round" stroke="#000" fill="none">
                                        <path d="M1200 600V100"/>
                                        <path opacity=".5" d="M1200 2300v-500"/>
                                        <path opacity=".917" d="M900 680.4l-250-433"/>
                                        <path opacity=".417" d="M1750 2152.6l-250-433"/>
                                        <path opacity=".833" d="M680.4 900l-433-250"/>
                                        <path opacity=".333" d="M2152.6 1750l-433-250"/>
                                        <path opacity=".75" d="M600 1200H100"/>
                                        <path opacity=".25" d="M2300 1200h-500"/>
                                        <path opacity=".667" d="M680.4 1500l-433 250"/>
                                        <path opacity=".167" d="M2152.6 650l-433 250"/>
                                        <path opacity=".583" d="M900 1719.6l-250 433"/>
                                        <path opacity=".083" d="M1750 247.4l-250 433"/>
                                        <animateTransform
                                            attributeName="transform"
                                            attributeType="XML" type="rotate"
                                            keyTimes="0;0.08333;0.16667;0.25;0.33333;0.41667;0.5;0.58333;0.66667;0.75;0.83333;0.91667"
                                            values="0 1199 1199;30 1199 1199;60 1199 1199;90 1199 1199;120 1199 1199;150 1199 1199;180 1199 1199;210 1199 1199;240 1199 1199;270 1199 1199;300 1199 1199;330 1199 1199"
                                            dur="0.83333s" begin="0s" repeatCount="indefinite" calcMode="discrete"
                                        />
                                    </g>
                                </svg>&nbsp;Saving...&nbsp;</h6>
                        </div>
                        <div class="panel panel-body order_notes_plain" id="order_notes_plain_{{ order.id }}">
                            {{ order.cs_notes|default_if_none:""|safe }}
                        </div>
                        <div class="panel panel-body hidden order_notes_editor" id="order_notes_editor_{{ order.id }}">
                            <form id="order_notes_form" action="{% url 'cs_update_notes' order.id %}" method="post">
                                <textarea rows="6" style="width: 100%" id="editor_{{ order.id }}" class="editor">{{ order.cs_notes|default_if_none:""|safe }}</textarea>
                                <button type="button" oid="{{ order.id }}" class="btn pull-right order_notes_editor_submit">Save</button>
                            </form>
                        </div>
                    </div>
                </div>

            </div>
        {% endif %}
        <!--contact details -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading clearfix">
                        <h3 class="panel-title pull-left">Connected items in production and invoices </h3>
                    </div>

                    {% include 'components/products.html' with products=products order=order cs=cs product_statuses_to_cancel=product_statuses_to_cancel order_statuses_to_cancel=order_statuses_to_cancel %}
                    {% include 'components/invoices.html' with order=order cs=cs %}

                    {% if cs == False %}
                        {% for mt in order.moneytransfer_set.all %}
                        <div class="list-group">
                            <div class="list-group-item">
                                <label> <a href="/admin/accounting/moneytransfer/{{ mt.id }}/">Money transfer: {{ mt.get_payment_source_display }} {{ mt.get_payment_type_display }} <br/> Issued at: {{ mt.issue_date }}</a></label>
                                <p>Amount: {{ mt.amount }} {{ mt.get_currency_display }}</p>
                            </div>
                        </div>
                        {% empty %}
                        <div class="list-group">
                            <div class="list-group-item">
                                <label>Missing money tranfers</label>
                            </div>
                        </div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
        </div>

        {% if cs == False %}
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Checks</h3>
                        </div>
                        <div class="list-group {% if check_totals.is_ok == False %}bg-danger-important{% endif %}">
                            <div class="list-group-item {% if check_totals.is_ok == False %}bg-danger-important{% endif %}">
                                <label>Invoices vs money transfers - total</label>
                                <p>{{ check_totals.reason|safe }}</p>
                            </div>
                        </div>
                        <div class="list-group {% if check_dates.is_ok == False %}bg-danger-important{% endif %}">
                            <div class="list-group-item {% if check_dates.is_ok == False %}bg-danger-important{% endif %}">
                                <label>Money transfer vs invoice(with correction)sale data check</label>
                                <p>{{ check_dates.reason|safe }}</p>
                                {% for pair in check_dates.matched %}
                                    <p >Matched: {{ pair.0.pretty_id }} with {{ pair.1.issue_date }} for {{ pair.1.amount }} {{ pair.1.get_currency_display }}</p>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Actions</h3>
                        </div>
                        <div class="list-group">
                            <div class="list-group-item ">
                                <a href="{% url 'cs_create_proforma' order.id %}" class="btn btn-info">Create proforma (without sending)</a>
                                <a href="{% url 'cs_sent_proforma' order.id %}" class="btn btn-info">Create and sent proforma to {{ order.email }}</a>
                                <a href="{% url 'cs_update_order' order.id %}" class="btn btn-info">Update order data</a>
                                <a href="{% url 'cs_create_free_return' order.id %}" class="btn btn-info">Create Free Return</a>
                                <a href="{% url 'cs_send_order_notifications' order.id %}" class="btn btn-info">Send Order Notifications</a>
                                <form class="inline-form" action="{% url 'cs_design_changed' order.id %}" method="post">
                                    <button type="submit" oid="{{ order.id }}" class="btn btn-info">
                                        Design changed
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
        {% if order.get_free_return_status is not None %}
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default" id="free_return_{{ order.id }}">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Free return status</h3>
                        </div>
                        <div class="list-group">
                            {% for key, item in order.get_free_return_data.items %}
                            <div class="list-group-item ">
                                <span class="badge">{{ item.status|join:"<br/>" }}</span>
                                <span class="badge">{{ item.note|join:", " }}</span>
                                <h4 class="list-group-item-heading">Free return id: {{ key }}</h4>
                                <h5 class="list-group-item-text">
                                    {{ item.items|join:"<br/>" }}
                                </h5>
                                <p class="list-group-item-text">Reason:</p>
                                <p class="list-group-item-text">{{ item.reason|join:"<hr/>" }}</p>
                                <p class="list-group-item-text">Is packed: {{ item.is_packed|yesno:"Yes,No"}}</p>
                                <p class="list-group-item-text">Need packaging materials: {{ item.is_need_packaging|yesno:"Yes,No"}}</p>
                                <p class="list-group-item-text">Ready to pickup: {{ item.is_send_asap|yesno:"Yes,No"}}</p>
                                <p class="list-group-item-text">Return Assembly Price: {{ item.return_assembly_price}}{{ order_currency.symbol }}</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
        </div>
    </div><!--div class="row"-->

    <div class="row">
        <div class="col-sm-12 bootcards-cards">
            <!--contact details -->
            <div id="CartList">
                <div class="panel panel-default">
                    <div class="panel-heading clearfix">
                        <h3 class="panel-title pull-left">Order details</h3>
                    </div>
                    <div class="list-group">
                    {% if cs == False %}
                        <a class="list-group-item" href="/admin/orders/order/{{ order.id }}/">
                    {% else %}
                        <div class="list-group-item">
                    {% endif %}
                        <div class="row">
                            <div class="col-sm-3">
                                <p class="list-group-item-text"> Netto: {{ order.total_price_net|safe }} €</p>
                                <p class="list-group-item-text"> Brutto: {{ order.total_price|safe }} €</p>
                                <p class="list-group-item-text"> Promo amount: {{ order.promo_amount|safe }} €</p>
                                <p class="list-group-item-text"> Promo text: {{ order.promo_text|default:"without promo" }}</p>
                            </div>
                            <div class="col-sm-3">
                                {% with order_currency.symbol as currency %}
                                    <p class="list-group-item-text"> Regionalized Netto: {{ order.region_total_price_net|safe }} {{ currency }}</p>
                                    <p class="list-group-item-text"> Regionalized Brutto: {{ order.region_total_price|safe }} {{ currency }}</p>
                                    <p class="list-group-item-text"> Regionalized Promo amount: {{ order.region_promo_amount|safe }} {{ currency }}</p>
                                    <p class="list-group-item-text"> Regionalized Promo text: {{ order.promo_text|default:"without promo" }}</p>
                                {% endwith %}
                            </div>
                            <div class="col-sm-3">
                                <p class="list-group-item-text">{{ order.get_items_as_string|safe }}</p>
                            </div>
                            {% if not order.order_or_product_in_status_production_or_above %}
                                <div class="col-sm-3">
                                    <p>
                                        <a href="{% url 'cs_push_to_production' order.id %}" class="btn btn-info">
                                            Push to production
                                        </a>
                                    </p>
                                </div>
                            {% endif %}
                        </div>
                    {% if cs == False %}
                        </a>
                    {% else %}
                        </div>
                    {% endif %}
                    </div>
                    {% for item in order.items.all %}
                    <div class="list-group">
                        <div class="list-group-item" >
                        {% if order.is_target_attached_to_order and item.is_target_for_switch %}
                            <div class="row source-order-item">
                                <div class="col-sm-2">
                                    <p class="list-group-item-text">
                                        <a target="_blank" href="{{ item.order_item.get_item_url }}">
                                            Gallery Id: {{ order.source_order_item.order_item.id }}
                                        </a>
                                    </p>
                                    {% with order.source_order_item.product_set.all_with_deleted.first as product %}
                                        {% if product %}
                                            <p class="list-group-item-text">Shelf Id: {{ product.pk }}</p>
                                        {% endif %}
                                    {% endwith %}
                                    <p class="list-group-item-text">Created at: {{ order.source_order_item.order_item.created_at|naturalday }}</p>
                                    <p class="list-group-item-text">Quantity: {{ item.quantity }}</p>
                                </div>
                                <div class="col-sm-1">
                                    {% with order_currency.symbol as currency %}
                                        <p class="list-group-item-text"> Regionalized:</p>
                                    <p class="list-group-item-text"> Netto: {{ order.source_order_item.region_price_net|safe }} {{ currency }}</p>
                                    <p class="list-group-item-text"> Brutto: {{ order.source_order_item.region_price|safe }} {{ currency }}</p>
                                    {% endwith %}
                                </div>
                                <div class="col-sm-1">
                                    <p class="list-group-item-text"> In Euro:</p>
                                    <p class="list-group-item-text"> Netto: {{ order.source_order_item.price_net|safe }} €</p>
                                    <p class="list-group-item-text"> Brutto: {{ order.source_order_item.price|safe }} €</p>
                                </div>

                                <div class="col-sm-6">
                                    <img class="item__order_item__preview" src="{{ order.source_order_item.order_item.preview | file_url }}">
                                </div>
                                <div class="col-sm-2">
                                    <p class="list-group-item-text"><a href="/admin/gallery/{{ order.source_order_item.order_item.get_furniture_type }}/{{ order.source_order_item.order_item.id }}/">See shelf</a></p>
                                </div>
                            </div>
                        {% endif %}
                            <div class="row">
                                <div class="col-sm-2">
                                    <p class="list-group-item-text">
                                        <a target="_blank" href="{{ item.order_item.get_item_url }}">
                                            Gallery Id: {{ item.order_item.id }}
                                        </a>
                                    </p>
                                    {% with item.product_set.first as product %}
                                        {% if product %}
                                            <p class="list-group-item-text">
                                                <a target="_blank" href="{% url 'admin:producers_product_change' product.pk %}">
                                                    Shelf Id: {{ product.pk }}
                                                </a>
                                            </p>
                                        {% endif %}
                                    {% endwith %}
                                    <p class="list-group-item-text">Created at: {{ item.order_item.created_at|naturalday }}</p>
                                    <p class="list-group-item-text">Quantity: {{ item.quantity }}</p>

                                    {% if item.order_item.description %}
                                        <p class="list-group-item-text">Item edited by CS: {{ item.order_item.description }}</p>
                                    {% endif %}
                                </div>
                                <div class="col-sm-1">
                                    {% with order_currency.symbol as currency %}
                                        <p class="list-group-item-text"> Regionalized:</p>
                                        <p class="list-group-item-text"> Netto: {{ item.region_price_net|safe }} {{ currency }}</p>
                                        <p class="list-group-item-text"> Brutto: {{ item.region_price|safe }} {{ currency }}</p>
                                    {% endwith %}
                                </div>
                                <div class="col-sm-1">
                                    <p class="list-group-item-text"> In Euro:</p>
                                    <p class="list-group-item-text"> Netto: {{ item.price_net|safe }} €</p>
                                    <p class="list-group-item-text"> Brutto: {{ item.price|safe }} €</p>
                                </div>
                                <div class="col-sm-6">
                                    <img class="item__order_item__preview" src="{{ item.order_item.preview|file_url }}">
                                </div>

                                <div class="col-sm-2">
                                    <p class="list-group-item-text">
                                        <a href="/admin/gallery/{{ item.order_item.get_furniture_type }}/{{ item.order_item.id }}/">See shelf</a>
                                    </p>
                                    {% if item.product_is_batched %}
                                        <p class="list-group-item-text">Can't be switched - already batched!</p>
                                    {% else %}
                                        <p class="list-group-item-text">
                                            {% if order.is_start_switch_allowed and item.quantity == 1 %}
                                                <form action="{% url 'cs_order_switch_item_on_hold' order.id %}" method="POST">
                                                    {% csrf_token %}
                                                    <input type="hidden" id="source_order_item" name="source_order_item" value="{{ item.pk }}">
                                                    <button class="btn btn-info btn-prevent-doubleclick" type="submit">Automatic Switch</button>
                                                </form>
                                            {% else %}
                                                {% if order.is_pending_switch_type_correction_request and not order.should_wait_for_additional_payment %}
                                                    There is still pending correction request for previous switch, please accept, so you can process next switch
                                                {% elif item.is_source_for_switch or item.is_target_for_switch %}
                                                    <a href="{% url order.get_absolute_switch_url order.id %}" class="btn btn-info">Continue Automatic Switch</a>
                                                {% elif order.is_klarna_payment %}
                                                    You can't switch item paid with klarna method.
                                                {% elif order.status != 3 %}
                                                    Can't be switched - status {{ order.get_status_display }}.
                                                {% elif order.is_extra_discount_in_progress %}
                                                    Add Extra discount in progress.
                                                {% elif item.quantity > 1 %}
                                                    Can't be switched - these items have quantity greater than one.
                                                {% elif order.is_switch_status_in_progress %}
                                                    Currently other item is during switch mode.
                                                {% endif %}
                                            {% endif %}
                                        </p>
                                    {% endif %}
                                     <p>
                                        <a href="{% url 'cs_generate_instructions' item.id %}" class="btn btn-info">
                                            Generate instructions
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    {% if order.assembly %}
                        <div class="list-group">
                            <div class="list-group-item" >
                                <div class="row">
                                    <div class="col-sm-2">
                                        <p class="list-group-item-text">Assembly</p>
                                    </div>
                                    <div class="col-sm-2">
                                        {% with order_currency.symbol as currency %}
                                            <p class="list-group-item-text">Regionalized:</p>
                                            <p class="list-group-item-text">Brutto: {{ order.get_assembly_price|safe }} {{ currency }}</p>
                                        {% endwith %}
                                    </div>
                                    <div class="col-sm-2">
                                        <p class="list-group-item-text">In Euro:</p>
                                        <p class="list-group-item-text">Brutto: {{ order.get_assembly_price_in_euro|safe }} €</p>
                                    </div>

                                    <div class="col-sm-4">
                                        <i style="width: 270px; height:200px; font-size: 128px" class="fa fa fa-gavel"></i>
                                    </div>
                                    <div class="col-sm-2"></div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                        <div class="list-group">
                            <div class="list-group-item" >
                                <div class="row">
                                    <div class="col-sm-6">
                                        <p class="list-group-item-text">
                                            <button data-toggle="modal" data-target="#addSampleBoxToCartModal" data-order_id="{{ order.id }}">
                                                {% if order.status == 9 %}Add sample box to cart{% else %} Add sample box to already paid order{% endif %}
                                            </button>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="row">
                                    <div class="col-sm-10">
                                        <p class="list-group-item-text">Emails send to customer </p>
                                        {% for email in mails %}
                                            <p class="list-group-item-text">
                                                Sent at: <b>{{ email.date_created|default_if_none:"" }}</b> Subject: <b>{{ email.subject }}</b>
                                            </p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% if cs == False %}
                            <div class="panel-footer">
                                <a class="btn btn-link btn-xs pull-right" href="/admin/auth/orders/">
                                    <small class="pull-left">Go to orders</small>
                                </a>
                            </div>
                        {% endif %}
                        </div>
                </div><!--contact card-->
            </div><!--list-details-->
        </div><!--div class="row"-->
    </div><!-- div class="panel-body" -->
</div>
