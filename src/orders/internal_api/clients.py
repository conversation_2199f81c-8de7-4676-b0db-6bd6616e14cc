from typing import TYPE_CHECKING

from django.conf import settings

from apiclient import exceptions
from apiclient.client import APIClient
from apiclient.decorates import endpoint
from apiclient.error_handlers import Base<PERSON>rrorHandler
from apiclient.response import Response

from custom.internal_api.clients import JsonAPIClient
from custom.internal_api.deserializers import LogisticOrderDeserializer
from custom.internal_api.enums import AssemblyTypeChoices
from orders.internal_api.serializers import (
    ForLogisticOrderSerializer,
    LogisticOrderAssemblyTypeSerializer,
)
from producers.choices import ProductStatus
from producers.internal_api.serializers import ForLogisticProductSerializer

if TYPE_CHECKING:
    from complaints.models import Complaint
    from orders.models import Order
    from producers.models import Product


class ClientError(Exception):
    def __init__(self, errors):
        self.errors = errors


class VerboseErrorHandler(BaseErrorHandler):
    @staticmethod
    def get_exception(response: Response) -> exceptions.APIRequestError:
        """Parses client errors to extract bad request reasons."""
        if 400 <= response.get_status_code() < 500:
            data = response.get_json()
            errs = data.get('errors') if data.get('errors') else [data.get('error')]
            return ClientError(errs)

        return exceptions.APIRequestError('something went wrong')


@endpoint(base_url=settings.LOGISTIC_URL)
class LogisticOrderAPIEndpoint(APIClient):
    list = 'internal-api/v1/logistic-orders/'
    order_list = 'internal-api/v1/logistic-orders/orders/'
    orders_eco_tax = 'internal-api/v1/logistic-orders/orders-for-eco-tax/'
    orders_delivered = 'internal-api/v1/logistic-orders/orders-delivered/'
    klarna_orders_delivered = (
        'internal-api/v1/logistic-orders/delivered-klarna-orders-for-capture/'
    )
    orders_sent_not_delivered = (
        'internal-api/v1/logistic-orders/orders-sent-not-delivered/'
    )
    orders_sent_and_delivered = (
        'internal-api/v1/logistic-orders/orders-sent-and-delivered/'
    )
    kpi_leadtime_orders = 'internal-api/v1/logistic-orders/kpi-leadtime-orders/'
    grouped_by_order_for_leadtime = (
        'internal-api/v1/logistic-orders/grouped-by-order-for-leadtime/'
    )
    emails_from_free_returns = (
        'internal-api/v1/logistic-orders/emails-from-free-returns/'
    )
    product_shipped_flow = 'internal-api/v1/logistic-orders/product-shipped-flow/'
    complaint_reproduction_shipped_flow = (
        'internal-api/v1/logistic-orders/complaint-reproduction-shipped-flow/'
    )
    product_to_be_shipped_flow = (
        'internal-api/v1/logistic-orders/product-to-be-shipped-flow/'
    )
    sample_post_delivery_one_flow = (
        'internal-api/v1/logistic-orders/sample-post-delivery-one-flow/'
    )

    create_for_complaint = 'internal-api/v1/logistic-orders/for-complaint/'
    refresh_serialized_product = (
        'internal-api/v1/logistic-orders/{}/set-serialized-product/'
    )
    delete_serialized_product = (
        'internal-api/v1/logistic-orders/{}/delete-serialized-product/'
    )
    delete_logistic_order = 'internal-api/v1/logistic-orders/{}/delete/'
    invalidate_get_similar_orders_cache = (
        'internal-api/v1/logistic-orders/invalidate-get-similar-orders/'
    )
    refresh_serialized_order = (
        'internal-api/v1/logistic-orders/{}/set-serialized-order/'
    )
    refresh_serialized_order_for_all_logistic_orders = (
        'internal-api/v1/logistic-orders/set-serialized-order-for-all-logistic-orders/'
    )
    set_to_be_shipped = 'internal-api/v1/logistic-orders/{}/set-to-be-shipped/'
    rollback_to_be_shipped = (
        'internal-api/v1/logistic-orders/{}/rollback-to-be-shipped/'
    )
    set_assembly_type = 'internal-api/v1/logistic-orders/{}/set-assembly-type/'
    set_undelivered = 'internal-api/v1/logistic-orders/{}/set-undelivered/'
    disable_mailing = 'internal-api/v1/logistic-orders/{}/disable-mailing/'
    create_from_free_returns = (
        'internal-api/v1/logistic-orders/create-from-free-returns/'
    )
    split_logistic_orders = 'internal-api/v1/logistic-orders/split-logistic-orders/'
    select_best_transport = (
        'internal-api/v1/logistic-orders/{id}/select-best-transport/'
    )
    request_estimated_cost_assembly = (
        'internal-api/v1/logistic-orders/{}/create-estimated-assembly-costs/'
    )
    request_delivery_report = (
        'internal-api/v1/request-proposed-delivery-for-products-report/'
    )
    move_products_from_logistic_order = (
        'internal-api/v1/logistic-orders/{}/move-products-from-logistic-order/'
    )


@endpoint(base_url=settings.LOGISTIC_URL)
class ShippersApiEndpoint(APIClient):
    get_delivery_region_info = 'internal-api/v1/get-delivery-offset/'


class ShippersApiClient(JsonAPIClient):
    def get_delivery_region_info(self):
        response = self.get(
            ShippersApiEndpoint.get_delivery_region_info,
        )
        return response


class LogisticOrderAPIClient(JsonAPIClient):
    def rollback_to_be_shipped(self, logistic_order_id: int) -> dict:
        response = self.post(
            LogisticOrderAPIEndpoint.rollback_to_be_shipped.format(logistic_order_id),
            data={},
        )
        return response

    def set_to_be_shipped(self, logistic_order_id: int):
        response = self.post(
            LogisticOrderAPIEndpoint.set_to_be_shipped.format(logistic_order_id),
            data={},
        )
        return response

    def set_assembly_type(
        self, logistic_order_id: int, assembly_type: AssemblyTypeChoices | None
    ):

        response = self.post(
            LogisticOrderAPIEndpoint.set_assembly_type.format(logistic_order_id),
            data=LogisticOrderAssemblyTypeSerializer(assembly_type).data,
        )
        return response

    def set_undelivered(self, logistic_order_id: int):
        response = self.post(
            LogisticOrderAPIEndpoint.set_undelivered.format(logistic_order_id),
            data={},
        )
        return response

    def request_estimated_cost_assembly(self, logistic_order_id: int):
        response = self.post(
            LogisticOrderAPIEndpoint.request_estimated_cost_assembly.format(
                logistic_order_id
            ),
            data={},
        )
        return response

    def disable_mailing(self, logistic_order_id: int):
        response = self.post(
            LogisticOrderAPIEndpoint.disable_mailing.format(logistic_order_id),
            data={},
        )
        return response

    def split(self, order: 'Order'):
        serialized_order = ForLogisticOrderSerializer(order).data
        response = self.post(
            LogisticOrderAPIEndpoint.split_logistic_orders,
            data={'serialized_order': serialized_order},
        )
        return response

    def refresh_serialized_product(self, product: 'Product'):
        serialized_product = ForLogisticProductSerializer(product).data
        response = self.post(
            LogisticOrderAPIEndpoint.refresh_serialized_product.format(
                product.logistic_order
            ),
            data={'serialized_product': serialized_product},
        )
        return response

    def delete_serialized_product(self, product: 'Product'):
        response = self.post(
            LogisticOrderAPIEndpoint.delete_serialized_product.format(
                product.logistic_order
            ),
            data={'serialized_product_id': product.pk},
        )
        return response

    def request_delivery_report(self, order_ids, manufactor, email):
        return self.post(
            LogisticOrderAPIEndpoint.request_delivery_report,
            data={'order_ids': order_ids, 'manufactor': manufactor, 'email': email},
        )

    def delete_logistic_order(self, logistic_order_id: int):
        response = self.delete(
            LogisticOrderAPIEndpoint.delete_logistic_order.format(logistic_order_id),
        )
        return response

    def invalidate_get_similar_orders_cache(self):
        return self.post(
            LogisticOrderAPIEndpoint.invalidate_get_similar_orders_cache,
            data={},
        )

    def refresh_serialized_order(self, logistic_order_id: int, order: 'Order'):
        serialized_order = ForLogisticOrderSerializer(order).data

        response = self.post(
            LogisticOrderAPIEndpoint.refresh_serialized_order.format(logistic_order_id),
            data={'serialized_order': serialized_order},
        )
        return response

    def refresh_serialized_order_for_all_logistic_orders(self, order: 'Order'):
        serialized_order = ForLogisticOrderSerializer(order).data

        response = self.post(
            LogisticOrderAPIEndpoint.refresh_serialized_order_for_all_logistic_orders,
            data={'serialized_order': serialized_order},
        )
        return response

    def get_or_create_logistic_order(self, order: 'Order'):
        serialized_order = ForLogisticOrderSerializer(order).data
        response = self.post(
            LogisticOrderAPIEndpoint.list,
            data={'serialized_order': serialized_order},
        )
        return response

    def get_or_create_logistic_order_for_complaint(self, complaint: 'Complaint'):
        serialized_order = ForLogisticOrderSerializer(complaint.reproduction_order).data
        serialized_products = []
        if complaint.reproduction_product:
            serialized_products = [
                ForLogisticProductSerializer(complaint.reproduction_product).data
            ]

        response = self.post(
            LogisticOrderAPIEndpoint.create_for_complaint,
            data={
                'serialized_order': serialized_order,
                'serialized_products': serialized_products,
                'to_be_shipped': complaint.express_replacement,
            },
        )
        return response

    def filter_by_delivered_date(self, months_back):
        return self.get(
            LogisticOrderAPIEndpoint.order_list, params={'months_back': months_back}
        )['order_ids']

    def filter_orders_for_eco_tax(self, start_date, end_date, order_type=None):
        data = {'start_date': str(start_date), 'end_date': str(end_date)}
        if order_type:
            data['order_type'] = order_type

        return self.post(LogisticOrderAPIEndpoint.orders_eco_tax, data=data)[
            'order_ids'
        ]

    def filter_delivered_orders(self):
        return self.get(LogisticOrderAPIEndpoint.orders_delivered)['order_ids']

    def filter_delivered_klarna_orders_for_capture(self):
        return self.get(LogisticOrderAPIEndpoint.klarna_orders_delivered)['order_ids']

    def filter_sent_not_delivered_orders(self):
        return self.get(LogisticOrderAPIEndpoint.orders_sent_not_delivered)['order_ids']

    def filter_sent_and_delivered_orders(self):
        return self.get(LogisticOrderAPIEndpoint.orders_sent_and_delivered)['order_ids']

    def filter_kpi_leadtime_orders(self, start_date):
        return self.get(
            LogisticOrderAPIEndpoint.kpi_leadtime_orders,
            params={'start_date': str(start_date)},
        )['order_ids']

    def grouped_by_order_for_leadtime(self, orders):
        return self.post(
            LogisticOrderAPIEndpoint.grouped_by_order_for_leadtime,
            data={'orders': [order.id for order in orders]},
        )['by_order_id']

    def emails_from_free_returns(self, only_samplebox_order_ids):
        return self.post(
            LogisticOrderAPIEndpoint.emails_from_free_returns,
            data={'only_samplebox_order_ids': only_samplebox_order_ids},
        )['emails']

    def get_product_shipped_flow(self):
        response = self.get(LogisticOrderAPIEndpoint.product_shipped_flow)
        serializer = LogisticOrderDeserializer(data=response, many=True)
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    def get_complaint_reproduction_shipped_flow(self):
        response = self.get(
            LogisticOrderAPIEndpoint.complaint_reproduction_shipped_flow
        )
        serializer = LogisticOrderDeserializer(data=response, many=True)
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    def get_product_to_be_shipped_flow(self):
        response = self.get(LogisticOrderAPIEndpoint.product_to_be_shipped_flow)
        serializer = LogisticOrderDeserializer(data=response, many=True)
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    def sample_post_delivery_one_flow(
        self, retargeting_blacklisted_emails, blacklisted_emails
    ):
        response = self.post(
            LogisticOrderAPIEndpoint.sample_post_delivery_one_flow,
            data={
                'retargeting_blacklisted_emails': retargeting_blacklisted_emails,
                'blacklisted_emails': blacklisted_emails,
            },
        )
        serializer = LogisticOrderDeserializer(data=response, many=True)
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    def select_best_transport(self, logistic_order_id: int):
        response = self.get(
            LogisticOrderAPIEndpoint.select_best_transport.format(id=logistic_order_id),
        )
        return response['transport']

    def create_from_free_returns(self, free_returns) -> None:
        from orders.internal_api.serializers import ForLogisticOrderSerializer
        from producers.internal_api.serializers import ForLogisticProductSerializer

        logistic_order_creation_data = []

        for free_return in free_returns.prefetch_related('orderitem_set'):
            products = []
            order_items = free_return.orderitem_set.all()
            order = order_items.first().order

            for order_item in order_items:
                products.append(
                    order_item.product_set.exclude(status=ProductStatus.ABORTED).first()
                )

            logistic_order_creation_data.append(
                {
                    'serialized_order': ForLogisticOrderSerializer(order).data,
                    'free_return_id': free_return.id,
                    'serialized_products': [
                        ForLogisticProductSerializer(product).data
                        for product in products
                    ],
                }
            )
        self.post(
            LogisticOrderAPIEndpoint.create_from_free_returns,
            data=logistic_order_creation_data,
        )

    def move_products_from_logistic_order(
        self, source_logistic_order_id: int, products_ids: list, status: int
    ):
        response = self.post(
            LogisticOrderAPIEndpoint.move_products_from_logistic_order.format(
                source_logistic_order_id
            ),
            data={
                'products_ids': products_ids,
                'status': status,
            },
        )
        return response
