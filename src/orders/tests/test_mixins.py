import datetime

from decimal import Decimal
from unittest import mock

from django.core import mail
from django.utils import timezone

import pytest

from custom.constants import VAT_EU
from custom.enums import Furniture
from custom.models import Countries
from customer_service.enums import (
    CSCorrectionRequestType,
    KlarnaPriceChangeType,
)
from gallery.enums import FurnitureStatusEnum
from invoice.choices import InvoiceStatus
from invoice.enums import InvoiceItemTag
from invoice.models import Invoice
from orders.enums import (
    OrderStatus,
    VatChoices,
)
from orders.exceptions import OrderItemAlreadyBatched
from orders.mixins import ExtraDiscountStatus
from orders.switch_status import (
    Price,
    SwitchStatus,
)
from producers.choices import (
    ProductPriority,
    ProductStatus,
)
from regions.enums import Currency


@pytest.mark.django_db
class TestOrderSwitchStatusTransitionsMixin:
    def test_process_item_on_hold_should_raise_error_when_order_item_batched(
        self,
        admin_user,
        order_item,
        product_factory,
        product_batch,
    ):
        product_factory(
            order=order_item.order,
            order_item=order_item,
            batch=product_batch,
        )

        with pytest.raises(OrderItemAlreadyBatched):
            order_item.order.commit_item_on_hold(order_item, by=admin_user)

    @mock.patch('producers.internal_api.events.ProductRefreshEvent.execute')
    def test_commit_item_on_hold_should_set_priority_on_hold_and_set_source_order_item_when_order_item_not_batched(
        self,
        mocked_product_refresh_event_execute,
        admin_user,
        order_item,
        product_factory,
    ):
        order_item_product = product_factory(
            order=order_item.order,
            order_item=order_item,
        )

        order_item.order.commit_item_on_hold(order_item, by=admin_user)

        order_item_product.refresh_from_db()
        assert order_item_product.priority == ProductPriority.ON_HOLD
        assert order_item.order.source_order_item == order_item

    @mock.patch('producers.internal_api.events.ProductRefreshEvent.execute')
    def test_rollback_item_on_hold_should_set_priority_normal_and_reset_source_order_item_when_on_hold(
        self,
        mocked_product_refresh_event_execute,
        admin_user,
        order_item,
        product_factory,
    ):
        order_item_product = product_factory(
            order=order_item.order,
            order_item=order_item,
        )

        order_item.order.commit_item_on_hold(order_item, by=admin_user)
        order_item.order.rollback_item_on_hold(admin_user, ProductPriority.NORMAL)

        order_item_product.refresh_from_db()
        assert order_item_product.priority == ProductPriority.NORMAL
        assert order_item.order.source_order_item is None

    @mock.patch('producers.internal_api.events.ProductRefreshEvent.execute')
    def test_commit_item_replacement_should_switch_source_with_target_and_recalculate_order(
        self,
        mocked_product_refresh_event_execute,
        admin_user,
        order_factory,
        order_item_factory,
        product_factory,
        jetty,
    ):
        order = order_factory(
            switch_status=SwitchStatus.BLANK.value,
            price_updated_at=timezone.now(),
            items=None,
            total_price=Decimal('100.0'),
            total_price_net=Decimal('80.0'),
            region_total_price=Decimal('100.0'),
            region_total_price_net=Decimal('80.0'),
        )
        source_order_item = order_item_factory(
            order=order,
            order_item__furniture_status=0,
            is_jetty=True,
        )
        source_product = product_factory(
            order=source_order_item.order,
            order_item=source_order_item,
            priority=ProductPriority.NORMAL,
        )

        order.commit_item_on_hold(source_order_item, by=admin_user)
        order.save()
        order.commit_item_replacement(jetty, quantity=1, by=admin_user)
        target_order_item = order.target_order_item
        order.save()
        source_product.refresh_from_db()
        order.refresh_from_db()

        assert source_product.priority == ProductPriority.ON_HOLD
        assert source_order_item.order_item.deleted
        assert source_order_item.deleted is not None

        assert order.items.filter(id=target_order_item.id).exists()
        assert not order.items.filter(pk=source_order_item.pk).exists()

        assert order.source_total_price == Decimal('100.0')
        assert order.source_total_price_net == Decimal('80.0')
        assert order.source_region_total_price == Decimal('100.0')
        assert order.source_region_total_price_net == Decimal('80.0')

    @mock.patch('producers.internal_api.events.ProductRefreshEvent.execute')
    def test_rollback_item_replacement_should_switch_back_target_with_source_and_recalculate_order(
        self,
        mocked_product_refresh_event_execute,
        admin_user,
        order_factory,
        order_item_factory,
        product_factory,
        jetty,
    ):
        order = order_factory(
            switch_status=SwitchStatus.BLANK.value,
            price_updated_at=timezone.now(),
            items=None,
            total_price=Decimal('100.0'),
            total_price_net=Decimal('80.0'),
            region_total_price=Decimal('100.0'),
            region_total_price_net=Decimal('80.0'),
        )
        source_order_item = order_item_factory(
            order=order,
            is_jetty=True,
            order_item__furniture_status=FurnitureStatusEnum.ORDERED,
        )

        source_product = product_factory(
            order=source_order_item.order,
            order_item=source_order_item,
            priority=ProductPriority.NORMAL,
        )

        order.commit_item_on_hold(source_order_item, by=admin_user)
        order.save()

        order.commit_item_replacement(jetty, quantity=1, by=admin_user)
        target_order_item = order.target_order_item
        order.save()

        order.rollback_item_replacement(by=admin_user)

        target_order_item.refresh_from_db()
        source_product.refresh_from_db()
        order.refresh_from_db()

        assert target_order_item.deleted is not None

        assert source_product.priority == ProductPriority.ON_HOLD
        assert source_order_item.deleted is None

        assert not order.items.filter(id=target_order_item.pk).exists()
        assert order.items.filter(pk=order.source_order_item.pk).exists()
        assert order.target_order_item is None
        assert order.source_order_item is not None

        assert order.source_total_price is None
        assert order.source_total_price_net is None
        assert order.source_region_total_price is None
        assert order.source_region_total_price_net is None

    @mock.patch('producers.internal_api.events.ProductDeletedEvent.execute')
    @mock.patch('producers.internal_api.events.ProductRefreshEvent.execute')
    @mock.patch('producers.models.Product.set_priority_from_order')
    def test_commit_additional_payment_should_set_product_to_normal_and_not_send_email(
        self,
        mocked_product_set_priority_from_order,
        mocked_product_refresh_event_execute,
        mocked_product_deleted_event_execute,
        admin_user,
        order_item_factory,
        product_factory,
    ):
        target_order_item = order_item_factory(
            order__switch_status=SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value
        )
        order = target_order_item.order
        product_factory(
            order=order,
            order_item=target_order_item,
            priority=ProductPriority.ON_HOLD,
        )
        order.target_order_item = target_order_item

        source_order_item = order_item_factory(order=order, is_jetty=True)

        source_product = product_factory(
            order=source_order_item.order,
            order_item=source_order_item,
            priority=ProductPriority.ON_HOLD,
        )
        order.source_order_item = source_order_item
        order.save()

        order.commit_additional_payment(by=admin_user)
        order.save()

        source_product.refresh_from_db()
        assert order.product_set.first().priority == ProductPriority.NORMAL
        mocked_product_set_priority_from_order.assert_called_once()
        assert len(mail.outbox) == 0
        assert source_product is not None

    @mock.patch('producers.internal_api.events.ProductRefreshEvent.execute')
    def test_commit_item_on_hold_should_change_product_priority_when_product_is_not_batched(
        self,
        mocked_product_refresh_event_execute,
        order_item,
        product_factory,
        admin_user,
    ):
        order_item_product = product_factory(
            order=order_item.order,
            order_item=order_item,
        )

        order_item.order.commit_item_on_hold(order_item, admin_user)
        order_item_product.refresh_from_db()

        assert order_item_product.priority == ProductPriority.ON_HOLD

    def test_commit_item_on_hold_should_raise_error_when_product_is_batched(
        self,
        order_item,
        product_factory,
        product_batch,
        admin_user,
    ):
        product_factory(
            order=order_item.order,
            order_item=order_item,
            batch=product_batch,
        )

        with pytest.raises(OrderItemAlreadyBatched):
            order_item.order.commit_item_on_hold(order_item, admin_user)

    @mock.patch('producers.internal_api.events.ProductRefreshEvent.execute')
    def test_rollback_item_on_hold_should_change_product_priority_and_set_source_order_item_to_none(
        self,
        mocked_product_refresh_event_execute,
        order_item_factory,
        product_factory,
        admin_user,
    ):
        order_item = order_item_factory(
            order__switch_status=SwitchStatus.ITEM_REPLACEMENT.value,
        )
        order = order_item.order
        order.source_order_item = order_item
        order.save(update_fields=['source_order_item'])

        product = product_factory(
            order=order_item.order,
            order_item=order_item,
            priority=ProductPriority.ON_HOLD,
        )

        order.rollback_item_on_hold(admin_user, ProductPriority.NORMAL)
        order.save()

        product.refresh_from_db()
        order.refresh_from_db()

        assert not order.source_order_item
        assert product.priority == ProductPriority.NORMAL

    def test_rollback_item_replacement_should_delete_target_item_and_rollback_source_item(
        self,
        order_factory,
        order_item_factory,
        admin_user,
    ):
        order = order_factory(
            switch_status=SwitchStatus.COST_RECALCULATION.value,
            price_updated_at=timezone.now(),
            source_total_price=Decimal('123.34'),
            source_total_price_net=Decimal('123.34'),
            items=None,
        )
        source_order_item = order_item_factory(order=order)
        source_order_item.delete()
        target_order_item = order_item_factory(order=order)

        order.source_order_item = source_order_item
        order.target_order_item = target_order_item

        order.rollback_item_replacement(admin_user)

        assert not order.target_order_item
        assert not order.source_total_price
        assert not order.source_total_price_net
        assert order.items.filter(id=source_order_item.id).exists()
        assert not order.items.filter(id=target_order_item.id).exists()

    @pytest.fixture()
    def order_to_recalculate(
        self,
        mocker,
        country_factory,
        order_factory,
        order_item_factory,
        product_factory,
        invoice_factory,
    ):
        germany_country = country_factory(germany=True)

        order = order_factory(
            chosen_payment_method='Master Card',
            status=OrderStatus.PAYMENT_PENDING,
            switch_status=SwitchStatus.COST_RECALCULATION.value,
            price_updated_at=timezone.now(),
            region=germany_country.region,
            country=Countries.germany.name,
            items=None,
            assembly=False,
            vat_rate=Decimal('0.0'),
            vat_type=VAT_EU,
            total_price=Decimal('80.0'),
            total_price_net=Decimal('80.0'),
            region_total_price=Decimal('80.0'),
            region_total_price_net=Decimal('80.0'),
        )

        source_order_item = order_item_factory(
            order=order,
            with_assembly=False,
            vat_amount=Decimal('0.0'),
            region_vat_amount=Decimal('0.0'),
            price=Decimal('80.0'),
            price_net=Decimal('80.0'),
            region_price=Decimal('80.0'),
            region_price_net=Decimal('80.0'),
            assembly_price=Decimal('10.0'),
            region_assembly_price=Decimal('10.0'),
            delivery_price=Decimal('0.0'),
            region_delivery_price=Decimal('0.0'),
        )
        source_order_item.delete()

        target_order_item = order_item_factory(
            order=order,
            with_assembly=False,
            vat_amount=Decimal('0.0'),
            region_vat_amount=Decimal('0.0'),
            price=Decimal('120.0'),
            price_net=Decimal('120.0'),
            region_price=Decimal('120.0'),
            region_price_net=Decimal('120.0'),
            assembly_price=Decimal('20.0'),
            region_assembly_price=Decimal('20.0'),
            delivery_price=Decimal('0.0'),
            region_delivery_price=Decimal('0.0'),
        )

        product_factory(
            order=order,
            order_item=source_order_item,
            priority=ProductPriority.ON_HOLD,
        )
        product_factory(
            order=order,
            order_item=target_order_item,
            priority=ProductPriority.ON_HOLD,
        )
        order.source_order_item = source_order_item
        order.target_order_item = target_order_item

        order.save()

        invoice_factory(pretty_id='test/1/2/3', order=order)

        mocker.patch('invoice.models.Invoice.save')
        mocker.patch('invoice.models.Invoice.delete')
        mocker.patch('orders.mixins.InvoiceCorrector.create_correction_request')

        return order

    @pytest.fixture()
    def france_order_no_target_product_to_recalculate(
        self,
        mocker,
        order_factory,
        order_item_factory,
        logistic_order_dto_factory,
        product_factory,
        invoice_factory,
        region_factory,
    ):
        logistic_order_dto = logistic_order_dto_factory(
            order_type=Furniture.jetty.value
        )

        order = order_factory(
            chosen_payment_method='Master Card',
            status=OrderStatus.IN_PRODUCTION,
            switch_status=SwitchStatus.COST_RECALCULATION.value,
            price_updated_at=timezone.now(),
            items=None,
            assembly=False,
            total_price=Decimal('100.0'),
            total_price_net=Decimal('80.0'),
            region_total_price=Decimal('100.0'),
            region_total_price_net=Decimal('80.0'),
            logistic_info=[logistic_order_dto],
            region=region_factory(france=True),
        )

        items = order_item_factory.create_batch(
            size=2,
            order=order,
            price=Decimal('100.0'),
            price_net=Decimal('80.0'),
            region_price=Decimal('100.0'),
            region_price_net=Decimal('80.0'),
        )
        product_factory(
            order=order,
            order_item=items[0],
            priority=ProductPriority.ON_HOLD,
        )

        order.source_order_item = items[0]
        order.target_order_item = items[1]

        order.save()

        invoice_factory(
            pretty_id='test/1/2/3',
            order=order,
        )

        mocker.patch('invoice.models.Invoice.save')
        mocker.patch('invoice.models.Invoice.delete')
        mocker.patch('orders.mixins.InvoiceCorrector.create_correction_request')

        return order

    @mock.patch('producers.internal_api.events.ProductDeletedEvent.execute')
    @mock.patch('orders.mixins.CreateProductFromOrderItem')
    @mock.patch('orders.internal_api.events.OrderRefreshEvent')
    @mock.patch(
        'orders.services.process_to_production.get_recycle_tax_value',
        return_value=Decimal('5.17'),
    )
    def test_commit_recalculations_should__calculate_recycle_tax_for_france(
        self,
        mock_get_recycle_tax_value,
        mock_order_refresh_event,
        mock_create_product_from_order_item_create,
        mock_product_deleted_event_execute,
        france_order_no_target_product_to_recalculate,
        product_factory,
        admin_user,
        region_factory,
    ):
        mock_create_product_from_order_item_create.return_value.create_return_value = product_factory(
            order=france_order_no_target_product_to_recalculate,
            order_item=france_order_no_target_product_to_recalculate.target_order_item,
            status=ProductStatus.NEW,
            cached_product_type=france_order_no_target_product_to_recalculate.target_order_item.content_type.model,
        )
        france_order_no_target_product_to_recalculate.source_total_price_net = Decimal(
            '80.0'
        )
        france_order_no_target_product_to_recalculate.source_total_price = Decimal(
            '80.0'
        )
        france_order_no_target_product_to_recalculate.source_region_total_price_net = (
            Decimal('80.0')
        )
        france_order_no_target_product_to_recalculate.source_region_total_price = (
            Decimal('80.0')
        )
        france_order_no_target_product_to_recalculate.save()

        options = {
            'should_add_assembly': False,
            'should_edit_assembly': False,
            'without_shelf_price_change': False,
            'without_assembly_price_change': False,
        }

        france_order_no_target_product_to_recalculate.commit_recalculations(
            options, by=admin_user, description=options
        )

        assert (
            france_order_no_target_product_to_recalculate.target_order_item.recycle_tax_value
            == Decimal('5.17')
        )

    @mock.patch('producers.internal_api.events.ProductDeletedEvent.execute')
    def test_commit_recalculations_should_not_change_price_when_all_options_are_false(
        self,
        mock_product_deleted_event_execute,
        order_to_recalculate,
        admin_user,
    ):
        order_to_recalculate.source_total_price = Decimal('80.0')
        order_to_recalculate.source_total_price_net = Decimal('80.0')
        order_to_recalculate.source_region_total_price = Decimal('80.0')
        order_to_recalculate.source_region_total_price_net = Decimal('80.0')
        order_to_recalculate.save()

        options = {
            'should_add_assembly': False,
            'should_edit_assembly': False,
            'without_shelf_price_change': False,
            'without_assembly_price_change': False,
        }

        order_to_recalculate.commit_recalculations(
            options, by=admin_user, description=options
        )

        assert not order_to_recalculate.assembly
        assert order_to_recalculate.total_price_after_switch_diff == Price(
            Decimal('0.0'), Decimal('0.0')
        )
        assert order_to_recalculate.region_total_price_after_switch_diff == Price(
            Decimal('0.0'), Decimal('0.0')
        )
        assert (
            order_to_recalculate.region_assembly_price_target_minus_source
            == Decimal('20.0')
        )

    @mock.patch('producers.internal_api.events.ProductDeletedEvent.execute')
    def test_commit_recalculations_change_price_when_assembly_is_true(
        self,
        mock_product_deleted_event_execute,
        order_to_recalculate,
        admin_user,
    ):
        order_to_recalculate.source_total_price_net = Decimal('80.0')
        order_to_recalculate.source_total_price = Decimal('80.0')
        order_to_recalculate.source_region_total_price_net = Decimal('80.0')
        order_to_recalculate.source_region_total_price = Decimal('80.0')
        order_to_recalculate.save()

        options = {
            'should_add_assembly': True,
            'should_edit_assembly': False,
            'without_shelf_price_change': False,
            'without_assembly_price_change': False,
        }

        order_to_recalculate.commit_recalculations(
            options, by=admin_user, description=options
        )

        assert order_to_recalculate.assembly
        assert order_to_recalculate.total_price_after_switch_diff == Price(
            Decimal('60.0'), Decimal('60.0')
        )
        assert order_to_recalculate.region_total_price_after_switch_diff == Price(
            Decimal('60.0'), Decimal('60.0')
        )
        assert (
            order_to_recalculate.region_assembly_price_target_minus_source
            == Decimal('10.0')
        )

    @mock.patch('producers.internal_api.events.ProductDeletedEvent.execute')
    def test_commit_recalculations_change_price_when_without_assembly_price_change_is_true(
        self,
        mock_product_deleted_event_execute,
        order_to_recalculate,
        admin_user,
    ):
        order_to_recalculate.assembly = True
        order_to_recalculate.source_total_price_net = Decimal('80.0')
        order_to_recalculate.source_total_price = Decimal('80.0')
        order_to_recalculate.source_region_total_price_net = Decimal('80.0')
        order_to_recalculate.source_region_total_price = Decimal('80.0')
        order_to_recalculate.save()

        options = {
            'should_add_assembly': False,
            'should_edit_assembly': True,
            'without_shelf_price_change': False,
            'without_assembly_price_change': True,
        }

        order_to_recalculate.commit_recalculations(
            options, by=admin_user, description=options
        )

        assert order_to_recalculate.assembly
        assert order_to_recalculate.total_price_after_switch_diff == Price(
            Decimal('50.0'), Decimal('50.0')
        )
        assert order_to_recalculate.region_total_price_after_switch_diff == Price(
            Decimal('50.0'), Decimal('50.0')
        )
        assert (
            order_to_recalculate.region_assembly_price_target_minus_source
            == Decimal('0.0')
        )

    @mock.patch('producers.internal_api.events.ProductDeletedEvent.execute')
    def test_commit_recalculations_should_change_price_only_by_assembly_price_when_without_shelf_price_change_is_true(
        self,
        product_deleted_event_execute_mock,
        order_to_recalculate,
        admin_user,
    ):
        order_to_recalculate.assembly = True
        order_to_recalculate.source_total_price_net = Decimal('80.0')
        order_to_recalculate.source_total_price = Decimal('80.0')
        order_to_recalculate.source_region_total_price_net = Decimal('80.0')
        order_to_recalculate.source_region_total_price = Decimal('80.0')
        order_to_recalculate.save()

        options = {
            'should_add_assembly': False,
            'should_edit_assembly': True,
            'without_shelf_price_change': True,
            'without_assembly_price_change': False,
        }

        order_to_recalculate.commit_recalculations(
            options, by=admin_user, description=options
        )

        assert order_to_recalculate.assembly
        assert order_to_recalculate.total_price_after_switch_diff == Price(
            Decimal('20.0'), Decimal('20.0')
        )
        assert order_to_recalculate.region_total_price_after_switch_diff == Price(
            Decimal('20.0'), Decimal('20.0')
        )
        assert (
            order_to_recalculate.region_assembly_price_target_minus_source
            == Decimal('10.0')
        )


@pytest.mark.django_db
class TestOrderSplitSampleAndFurnitureMixin:
    def test_check_if_voucher_applies_for_split_should_return_false_when_no_split(
        self, order_factory
    ):
        order = order_factory(parent_order=None)

        voucher_applies = order.check_if_voucher_applies_for_split()

        assert not voucher_applies

    def test_check_if_voucher_applied_for_split_should_return_false_when_suborder_for_parent_without_voucher(
        self, order_factory
    ):
        parent_order = order_factory(parent_order=None, used_promo=None)
        suborder = order_factory(parent_order=parent_order)

        voucher_applies = suborder.check_if_voucher_applies_for_split()

        assert not voucher_applies

    def test_check_if_voucher_applied_for_split_should_return_false_when_suborder_for_parent_with_absolute_voucher(
        self, order_factory, voucher_factory
    ):
        absolute_voucher = voucher_factory(is_absolute=True)
        parent_order = order_factory(parent_order=None, used_promo=absolute_voucher)
        suborder = order_factory(parent_order=parent_order)

        voucher_applies = suborder.check_if_voucher_applies_for_split()

        assert not voucher_applies

    def test_check_if_voucher_applies_for_split_should_return_true_when_suborder_for_parent_with_percentage_voucher(
        self, order_factory, voucher_factory
    ):
        percentage_voucher = voucher_factory(is_percentage=True)
        parent_order = order_factory(parent_order=None, used_promo=percentage_voucher)
        suborder = order_factory(parent_order=parent_order)

        voucher_applies = suborder.check_if_voucher_applies_for_split()

        assert voucher_applies

    def test_check_if_voucher_applies_for_split_should_return_false_when_parent_without_used_promo(
        self, order_factory, order_item_factory
    ):
        parent_order = order_factory(parent_order=None, used_promo=None)
        suborder = order_factory(
            parent_order=parent_order,
            country=Countries.united_kingdom.name,
            items=[],
        )
        order_item_factory(order=suborder, is_sample_box=True)

        voucher_applies = parent_order.check_if_voucher_applies_for_split()

        assert not voucher_applies

    def test_check_if_voucher_applies_for_split_should_return_false_when_parent_with_used_promo(
        self, order_factory, order_item_factory, voucher_factory
    ):
        percentage_voucher = voucher_factory(is_percentage=True)
        parent_order = order_factory(parent_order=None, used_promo=percentage_voucher)
        suborder = order_factory(
            parent_order=parent_order,
            country=Countries.united_kingdom.name,
            items=[],
        )
        order_item_factory(order=suborder, is_sample_box=True)

        voucher_applies = parent_order.check_if_voucher_applies_for_split()

        assert voucher_applies


@pytest.mark.django_db
class TestOrderAddExtraDiscountMixin:
    def test_commit_discount_recalculation_should_set_target_voucher_and_recalculate_when_no_switched(
        self,
        admin_user,
        region_germany_neutral,
        order_item_factory,
        order_factory,
        voucher_factory,
    ):
        source_voucher = voucher_factory(
            is_percentage=True, value=10, code='source_voucher'
        )
        target_voucher = voucher_factory(
            is_percentage=True,
            value=20,
            code='target_voucher',
            quantity=5,
            quantity_left=5,
        )

        order = order_factory(
            assembly=False,
            region=region_germany_neutral,
            used_promo=source_voucher,
            price_updated_at=timezone.now(),
            total_price=Decimal('90.0'),
            total_price_net=Decimal('90.0'),
            region_total_price=Decimal('90.0'),
            region_total_price_net=Decimal('90.0'),
            promo_amount=Decimal('10.0'),
            promo_amount_net=Decimal('10.0'),
            region_promo_amount=Decimal('10.0'),
            region_promo_amount_net=Decimal('10.0'),
            items=None,
            vat_type=VatChoices.VAT_EU,
            vat_rate=Decimal('0.0'),
        )

        order_item_factory(
            order=order,
            price=Decimal('100.0'),
            price_net=Decimal('100.0'),
            region_price=Decimal('100.0'),
            region_price_net=Decimal('100.0'),
            region_promo_value=Decimal('10.0'),
            vat_amount=Decimal('0.0'),
            region_vat_amount=Decimal('0.0'),
            delivery_price=Decimal('0.0'),
        )

        source_total_price = order.total_price
        source_total_price_net = order.total_price_net
        source_region_total_price = order.region_total_price
        source_region_total_price_net = order.region_total_price_net

        message_status, message_text = order.commit_discount_recalculation(
            target_voucher, by=admin_user
        )

        order.save()
        order.refresh_from_db()
        target_voucher.refresh_from_db()

        assert target_voucher.quantity_left == 4
        assert (
            order.extra_discount_status
            == ExtraDiscountStatus.DISCOUNT_RECALCULATION.value
        )
        assert order.used_promo == target_voucher
        assert source_voucher == order.completed_used_promos.last()

        assert order.source_extra_discount_total_price == source_total_price
        assert order.source_extra_discount_total_price_net == source_total_price_net
        assert (
            order.source_extra_discount_region_total_price == source_region_total_price
        )
        assert (
            order.source_extra_discount_region_total_price_net
            == source_region_total_price_net
        )

        expected_extra_discount = Price(Decimal('10.0'), Decimal('10.0'))
        assert order.total_price_difference == expected_extra_discount
        assert order.total_price == Decimal('80.0')
        assert order.total_price_net == Decimal('80.0')
        assert order.region_total_price == Decimal('80.0')
        assert order.region_total_price_net == Decimal('80.0')

        assert order.promo_amount == Decimal('20.0')
        assert order.promo_amount_net == Decimal('20.0')
        assert order.region_promo_amount == Decimal('20.0')
        assert order.region_promo_amount_net == Decimal('20.0')

    def test_commit_discount_recalculation_should_recalculate_switched_order_items(
        self,
        admin_user,
        region_germany_neutral,
        order_item_factory,
        order_factory,
        voucher_factory,
    ):
        source_voucher = voucher_factory(
            is_percentage=True,
            value=30,
            code='source_voucher',
        )
        target_voucher = voucher_factory(
            is_percentage=True,
            value=40,
            code='target_voucher',
        )

        order = order_factory(
            assembly=False,
            region=region_germany_neutral,
            used_promo=source_voucher,
            price_updated_at=timezone.now(),
            total_price=Decimal('1050.0'),
            total_price_net=Decimal('1050.0'),
            region_total_price=Decimal('1050.0'),
            region_total_price_net=Decimal('1050.0'),
            promo_amount=Decimal('450.0'),
            promo_amount_net=Decimal('450.0'),
            region_promo_amount=Decimal('450.0'),
            region_promo_amount_net=Decimal('450.0'),
            items=None,
            vat_type=VatChoices.VAT_EU,
            vat_rate=Decimal('0.0'),
        )

        target_order_item = order_item_factory(
            order=order,
            price=Decimal('1500.0'),
            price_net=Decimal('1500.0'),
            region_price=Decimal('1500.0'),
            region_price_net=Decimal('1500.0'),
            region_promo_value=Decimal('450.0'),
            vat_amount=Decimal('0.0'),
            region_vat_amount=Decimal('0.0'),
            delivery_price=Decimal('0.0'),
        )

        order.target_order_item = target_order_item
        order.completed_target_order_items.add(target_order_item)

        source_total_price = order.total_price
        source_total_price_net = order.total_price_net
        source_region_total_price = order.region_total_price
        source_region_total_price_net = order.region_total_price_net

        message_status, message_text = order.commit_discount_recalculation(
            target_voucher, by=admin_user
        )

        order.save()
        order.refresh_from_db()

        assert (
            order.extra_discount_status
            == ExtraDiscountStatus.DISCOUNT_RECALCULATION.value
        )
        assert order.used_promo == target_voucher
        assert source_voucher == order.completed_used_promos.last()
        assert target_order_item == order.completed_target_order_items.first()

        assert order.source_extra_discount_total_price == source_total_price
        assert order.source_extra_discount_total_price_net == source_total_price_net
        assert (
            order.source_extra_discount_region_total_price == source_region_total_price
        )
        assert (
            order.source_extra_discount_region_total_price_net
            == source_region_total_price_net
        )

        expected_extra_discount = Price(Decimal('150.0'), Decimal('150.0'))
        assert order.total_price_difference == expected_extra_discount
        assert order.total_price == Decimal('900.0')
        assert order.total_price_net == Decimal('900.0')
        assert order.region_total_price == Decimal('900.0')
        assert order.region_total_price_net == Decimal('900.0')

        assert order.promo_amount == Decimal('600.0')
        assert order.promo_amount_net == Decimal('600.0')
        assert order.region_promo_amount == Decimal('600.0')
        assert order.region_promo_amount_net == Decimal('600.0')

    def test_rollback_discount_recalculation_should_set_source_voucher_and_recalculate_back(
        self,
        admin_user,
        region_germany_neutral,
        order_item_factory,
        order_factory,
        voucher_factory,
    ):
        source_voucher = voucher_factory(
            is_percentage=True, value=10, code='source_voucher'
        )
        target_voucher = voucher_factory(
            is_percentage=True,
            value=20,
            code='target_voucher',
            quantity=5,
            quantity_left=5,
        )

        order = order_factory(
            assembly=False,
            region=region_germany_neutral,
            used_promo=source_voucher,
            price_updated_at=timezone.now(),
            total_price=Decimal('90.0'),
            total_price_net=Decimal('90.0'),
            region_total_price=Decimal('90.0'),
            region_total_price_net=Decimal('90.0'),
            promo_amount=Decimal('10.0'),
            promo_amount_net=Decimal('10.0'),
            region_promo_amount=Decimal('10.0'),
            region_promo_amount_net=Decimal('10.0'),
            items=None,
            vat_type=VatChoices.VAT_EU,
            vat_rate=Decimal('0.0'),
        )

        order_item_factory(
            order=order,
            price=Decimal('100.0'),
            price_net=Decimal('100.0'),
            region_price=Decimal('100.0'),
            region_price_net=Decimal('100.0'),
            region_promo_value=Decimal('10.0'),
            vat_amount=Decimal('0.0'),
            region_vat_amount=Decimal('0.0'),
            delivery_price=Decimal('0.0'),
        )

        order.commit_discount_recalculation(target_voucher, by=admin_user)
        order.save()
        order.refresh_from_db()

        order.rollback_discount_recalculation(by=admin_user)
        order.save()
        order.refresh_from_db()

        assert order.extra_discount_status == ExtraDiscountStatus.CANCELLED.value
        assert order.used_promo == source_voucher
        assert not order.completed_used_promos.exists()

        assert order.source_extra_discount_total_price is None
        assert order.source_extra_discount_total_price_net is None
        assert order.source_extra_discount_region_total_price is None
        assert order.source_extra_discount_region_total_price_net is None

        assert order.total_price == Decimal('90.0')
        assert order.total_price_net == Decimal('90.0')
        assert order.region_total_price == Decimal('90.0')
        assert order.region_total_price_net == Decimal('90.0')

        assert order.promo_amount == Decimal('10.0')
        assert order.promo_amount_net == Decimal('10.0')
        assert order.region_promo_amount == Decimal('10.0')
        assert order.region_promo_amount_net == Decimal('10.0')

    def test_commit_completed_should_create_klarna_adjustment_when_klarna(
        self,
        admin_user,
        region_germany_neutral,
        invoice_factory,
        order_item_factory,
        order_factory,
        voucher_factory,
    ):
        source_voucher = voucher_factory(
            is_percentage=True, value=10, code='source_voucher'
        )
        target_voucher = voucher_factory(
            is_percentage=True,
            value=20,
            code='target_voucher',
        )

        order = order_factory(
            assembly=False,
            chosen_payment_method='klarna',
            region=region_germany_neutral,
            used_promo=source_voucher,
            price_updated_at=timezone.now(),
            total_price=Decimal('90.0'),
            total_price_net=Decimal('90.0'),
            region_total_price=Decimal('90.0'),
            region_total_price_net=Decimal('90.0'),
            promo_amount=Decimal('10.0'),
            promo_amount_net=Decimal('10.0'),
            region_promo_amount=Decimal('10.0'),
            region_promo_amount_net=Decimal('10.0'),
            items=None,
            vat_type=VatChoices.VAT_EU,
            vat_rate=Decimal('0.0'),
        )

        order_item = order_item_factory(
            order=order,
            price=Decimal('100.0'),
            price_net=Decimal('100.0'),
            region_price=Decimal('100.0'),
            region_price_net=Decimal('100.0'),
            region_promo_value=Decimal('10.0'),
            vat_amount=Decimal('0.0'),
            region_vat_amount=Decimal('0.0'),
        )

        proforma = invoice_factory(
            pretty_id='PROFORMA/1/2',
            status=InvoiceStatus.PROFORMA,
            order=order,
        )

        order.commit_discount_recalculation(target_voucher, by=admin_user)
        order.save()
        order.refresh_from_db()

        klarna_adjustment = order.commit_completed(by=admin_user)
        order.save()
        order.refresh_from_db()

        assert order.extra_discount_status == ExtraDiscountStatus.COMPLETED.value
        assert klarna_adjustment.invoice == proforma
        assert klarna_adjustment.change_type == KlarnaPriceChangeType.DISCOUNT
        assert klarna_adjustment.amount == Decimal('10.0')
        assert (
            klarna_adjustment.reason
            == f'Add extra discount - 20.0%: {order_item.order_item}'
        )

    @mock.patch(
        'invoice.models.Invoice.get_exchange_date_and_rate', return_value=(None, 1)
    )
    def test_commit_completed_should_create_correction_request_when_no_klarna(
        self,
        get_exchange_date_and_rate__mocked,
        admin_user,
        region_germany_neutral,
        invoice_factory,
        order_item_factory,
        order_factory,
        voucher_factory,
    ):
        source_voucher = voucher_factory(
            is_percentage=True, value=10, code='source_voucher'
        )
        target_voucher = voucher_factory(
            is_percentage=True,
            value=20,
            code='target_voucher',
        )

        order = order_factory(
            assembly=False,
            chosen_payment_method='bank_transfer',
            region=region_germany_neutral,
            used_promo=source_voucher,
            price_updated_at=timezone.now(),
            total_price=Decimal('90.0'),
            total_price_net=Decimal('90.0'),
            region_total_price=Decimal('90.0'),
            region_total_price_net=Decimal('90.0'),
            promo_amount=Decimal('10.0'),
            promo_amount_net=Decimal('10.0'),
            region_promo_amount=Decimal('10.0'),
            region_promo_amount_net=Decimal('10.0'),
            items=None,
            vat_type=VatChoices.VAT_EU,
            vat_rate=Decimal('0.0'),
        )

        order_item_factory(
            order=order,
            price=Decimal('100.0'),
            price_net=Decimal('100.0'),
            region_price=Decimal('100.0'),
            region_price_net=Decimal('100.0'),
            region_promo_value=Decimal('10.0'),
            vat_amount=Decimal('0.0'),
            region_vat_amount=Decimal('0.0'),
        )

        invoice = invoice_factory(
            pretty_id='NORMAL/1/2',
            status=InvoiceStatus.ENABLED,
            order=order,
        )

        order.commit_discount_recalculation(target_voucher, by=admin_user)
        order.save()
        order.refresh_from_db()

        cs_correction_request = order.commit_completed(by=admin_user)
        order.save()
        order.refresh_from_db()

        assert order.extra_discount_status == ExtraDiscountStatus.COMPLETED.value
        assert cs_correction_request.issuer == admin_user
        assert cs_correction_request.correction_context == 'Add Extra Discount'
        assert (
            cs_correction_request.type_cs == CSCorrectionRequestType.TYPE_EXTRA_DISCOUNT
        )
        assert (
            cs_correction_request.tag
            == InvoiceItemTag.DISCOUNT_PRICE_ADJUSTMENT_DUE_TO_ANY_OTHER_REASON.value
        )
        assert cs_correction_request.invoice == invoice
        assert cs_correction_request.correction_amount_gross == Decimal('10.0')

    @mock.patch(
        'invoice.models.Invoice._generate_pretty_id_db_sequence', return_value='123'
    )
    @mock.patch(
        'invoice.models.Invoice.get_exchange_date_and_rate', return_value=(None, 1)
    )
    def test_create_invoice_should_set_additional_total_when_united_kingdom_lte_tax_threshould(
        self,
        mocked_get_exchange_date_and_rate,
        mocked__generate_pretty_id_db_sequence,
        order_factory,
        region_factory,
        country_factory,
    ):
        united_kingdom_country = country_factory(united_kingdom=True)
        united_kingdom_region = region_factory(
            united_kingdom=True,
            country=united_kingdom_country,
        )
        order = order_factory(
            region=united_kingdom_region,
            country=Countries.united_kingdom.name,
            region_total_price=Decimal('100.0'),
            vat_rate=Decimal('0.0'),
            paid_at=datetime.datetime(2024, 10, 10),
        )
        invoice = order.create_invoice()
        assert invoice.additional_total_text == Invoice.UNITED_KINGDOM_TAX_INCLUDED_TEXT
        assert invoice.additional_total_value == f'16.67{Currency.GBP.symbol}'

    def test_is_klarna_with_proforma_only_should_return_false_when_klarna_normal(
        self,
        order_factory,
        invoice_factory,
    ):
        order = order_factory(chosen_payment_method='klarna')
        invoice_factory(
            pretty_id='NORMAL/1/2',
            status=InvoiceStatus.ENABLED,
            order=order,
        )

        assert not order.is_klarna_with_proforma_only()

    def test_is_klarna_with_proforma_only_should_return_true_when_klarna_proforma(
        self,
        order_factory,
        invoice_factory,
    ):
        order = order_factory(chosen_payment_method='klarna')
        invoice_factory(
            pretty_id='PROFORMA/1/2',
            status=InvoiceStatus.PROFORMA,
            order=order,
        )

        assert order.is_klarna_with_proforma_only()
