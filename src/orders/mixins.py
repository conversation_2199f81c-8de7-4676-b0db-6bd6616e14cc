import enum
import logging

from copy import deepcopy
from decimal import Decimal
from typing import (
    TYPE_CHECKING,
    Optional,
)

from django.contrib.auth.models import User
from django.db import (
    models,
    transaction,
)
from django.utils import timezone

from django_fsm import (
    RETURN_VALUE,
    FSMField,
    transition,
)
from django_fsm_log.decorators import (
    fsm_log_by,
    fsm_log_description,
)
from sortedm2m.fields import SortedManyToManyField

from custom.enums import ChoicesMixin
from custom.internal_api.dto import LogisticOrderDTO
from custom.internal_api.enums import AssemblyTypeChoices
from custom.shelf_states_interactor import add_jetty_state_to_redis
from customer_service.correction_request_strategies import (
    CorrectionRequestAmountStrategy,
)
from customer_service.enums import (
    CSCorrectionRequestStatus,
    CSCorrectionRequestType,
    KlarnaPriceChangeType,
    KlarnaSource,
)
from customer_service.models import (
    CSCorrectionRequest,
    KlarnaAdjustment,
)
from gallery.enums import (
    FurnitureStatusEnum,
    ShelfStatusSource,
)
from invoice.choices import InvoiceStatus
from invoice.enums import InvoiceItemTag
from invoice.models import Invoice
from orders.enums import (
    OrderStatus,
    OrderSwitchInProgressStatus,
    OrderSwitchStartSwitchAllowedStatus,
    OrderSwitchTargetAttachedStatus,
)
from orders.exceptions import OrderItemAlreadyBatched
from orders.order_notes import (
    ASSEMBLY_TYPE_TO_NOTE,
    get_order_note,
    replace_assembly_type_order_note,
)
from orders.switch_status import (
    InvoiceCorrector,
    Price,
    SwitchStatus,
    can_order_item_be_switched,
    should_recalculate_order_again,
)
from pricing_v3.services.price_calculators import (
    OrderPriceCalculator,
    OrderPriceCalculatorForPriceUpdatedAtPricing,
)
from producers.choices import ProductPriority
from producers.internal_api.events import (
    ProductCreatedEvent,
    ProductDeletedEvent,
)
from producers.services.create_product import CreateProductFromOrderItem

if TYPE_CHECKING:
    from gallery.models.furniture_abstract import FurnitureAbstract
    from orders.models import (
        Order,
        OrderItem,
    )
    from producers.models import Product
    from vouchers.models import Voucher


logger = logging.getLogger('orders')


class OrderSwitchStatusTransitionsMixin(models.Model):
    switch_status = FSMField(
        default=SwitchStatus.BLANK.value,
        choices=SwitchStatus.choices(),
        db_index=True,
        protected=True,
    )
    switch_options = models.JSONField(default=dict, blank=True)

    source_order_item = models.OneToOneField(
        'orders.OrderItem',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='order_source',
    )
    target_order_item = models.OneToOneField(
        'orders.OrderItem',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='order_target',
    )
    completed_target_order_items = models.ManyToManyField(
        'orders.OrderItem',
        blank=True,
        related_name='completed_order_targets',
    )
    source_total_price = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )
    source_total_price_net = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )
    source_region_total_price = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )
    source_region_total_price_net = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )

    class Meta:
        abstract = True

    def is_switch_status_in_progress(self) -> bool:
        return self.switch_status in OrderSwitchInProgressStatus

    def check_if_voucher_applies_for_switch(self, voucher: 'Voucher') -> bool:
        return self.is_switch_status_in_progress() and self.used_promo == voucher

    def is_target_attached_to_order(self) -> bool:
        return self.switch_status in OrderSwitchTargetAttachedStatus

    def is_start_switch_allowed(self) -> bool:
        return (
            self.switch_status in OrderSwitchStartSwitchAllowedStatus
            and not self.is_pending_switch_type_correction_request()
            and not self.is_klarna_payment()
            and self.status == OrderStatus.IN_PRODUCTION
            and not self.is_extra_discount_in_progress()
        )

    def is_cancel_switch_allowed(self) -> bool:
        return (
            not self.is_accepted_switch_type_correction_request()
            and not self.is_klarna_payment()
            and self.status == OrderStatus.IN_PRODUCTION
        )

    def is_pending_switch_type_correction_request(self) -> bool:
        return CSCorrectionRequest.objects.filter(
            invoice__order=self,
            status=CSCorrectionRequestStatus.STATUS_NEW,
            type_cs=CSCorrectionRequestType.TYPE_SWITCH,
        ).exists()

    def is_accepted_switch_type_correction_request(self) -> bool:
        return CSCorrectionRequest.objects.filter(
            invoice__order=self,
            deleted_invoice_items__order_item__in=[self.source_order_item],
            status=CSCorrectionRequestStatus.STATUS_ACCEPTED,
            type_cs=CSCorrectionRequestType.TYPE_SWITCH,
        ).exists()

    def should_wait_for_additional_payment(self) -> bool:
        return self.switch_status == SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value

    def get_absolute_switch_url(self) -> str:
        from customer_service.views_switch import OrderSwitchWizardView

        return OrderSwitchWizardView.STATUS_TO_VIEW[self.switch_status]

    def refresh_from_db(self, using=None, fields=None) -> None:
        """
        Django-FSM raise error when you have field with protected=True
        and you refresh it
        """
        protected = []
        for field in self._meta.get_fields():
            if getattr(field, 'protected', False):
                protected.append(field)
                field.protected = False
        super().refresh_from_db(using=using, fields=fields)
        for field in protected:
            field.protected = True

    @property
    def total_price_after_switch_diff(self) -> Price:
        netto = self.total_price_net - self.source_total_price_net
        brutto = self.total_price - self.source_total_price
        return Price(brutto, netto)

    @property
    def region_total_price_after_switch_diff(self) -> Price:
        netto = self.region_total_price_net - self.source_region_total_price_net
        brutto = self.region_total_price - self.source_region_total_price
        return Price(brutto, netto)

    @property
    def price_target_minus_source(self) -> Price:
        netto = self.target_order_item.price_net - self.source_order_item.price_net
        brutto = self.target_order_item.price - self.source_order_item.price
        return Price(brutto, netto)

    @property
    def region_price_target_minus_source(self) -> Price:
        netto = (
            self.target_order_item.region_price_net
            - self.source_order_item.region_price_net
        )
        brutto = (
            self.target_order_item.region_price - self.source_order_item.region_price
        )
        return Price(brutto, netto)

    @property
    def region_assembly_price_target_minus_source(self) -> Decimal:
        source_order_item_assembly_price = (
            self.source_order_item.get_assembly_price()
            if self.assembly
            else Decimal('0.0')
        )
        return (
            self.target_order_item.get_assembly_price()
            - source_order_item_assembly_price
        )

    def _create_and_add_target_order_item(
        self,
        furniture_to_add: 'FurnitureAbstract',
        quantity: int,
    ) -> 'OrderItem':
        furniture_to_add.ordered_item.all().delete()

        # FIXME: add_item calculate order_item price, but we don't need it because,
        #  price is recalculated in next step
        target_order_item = self.add_item(furniture_to_add, quantity)
        furniture_to_add.owner = self.owner
        furniture_to_add.furniture_status = self.get_furniture_to_add_status()
        furniture_to_add.save()
        return target_order_item

    def _delete_order_item(self, order_item: 'FurnitureAbstract') -> None:
        self.remove_item(order_item.order_item)
        order_item.delete()

    def _undelete_order_item(
        self, order_item: 'FurnitureAbstract'
    ) -> 'FurnitureAbstract':
        order_item.undelete()
        self.unremove_item(order_item.order_item)
        return order_item

    def get_furniture_to_add_status(self) -> 'FurnitureStatusEnum':
        if self.status == OrderStatus.CART:
            return FurnitureStatusEnum.DRAFT
        return FurnitureStatusEnum.ORDERED

    def should_process_order_item_to_production(self) -> bool:
        return self.status == OrderStatus.IN_PRODUCTION

    def _copy_assembly_price_from_source(self, should_add_assembly: bool) -> None:
        if should_add_assembly:
            source_order_item_assembly_price = Decimal('0.0')
            source_order_item_region_assembly_price = Decimal('0.0')
        else:
            source_order_item_assembly_price = self.source_order_item.assembly_price
            source_order_item_region_assembly_price = (
                self.source_order_item.region_assembly_price
            )

        self.target_order_item.assembly_price = source_order_item_assembly_price
        self.target_order_item.region_assembly_price = (
            source_order_item_region_assembly_price
        )
        self.target_order_item.save(
            update_fields=['assembly_price', 'region_assembly_price']
        )
        self.items_changed_at = timezone.now()
        self.save(update_fields=['items_changed_at'])

    def _copy_order_item_price_from_source(self) -> None:
        self.target_order_item.price = self.source_order_item.price
        self.target_order_item.region_price = self.source_order_item.region_price

        self.target_order_item.price_net = self.source_order_item.price_net
        self.target_order_item.region_price_net = (
            self.source_order_item.region_price_net
        )

        self.target_order_item.vat_amount = self.source_order_item.vat_amount
        self.target_order_item.region_vat_amount = (
            self.source_order_item.region_vat_amount
        )

        self.target_order_item.delivery_price = self.source_order_item.delivery_price
        self.target_order_item.region_delivery_price = (
            self.source_order_item.region_delivery_price
        )
        self.target_order_item.region_promo_value = (
            self.source_order_item.region_promo_value
        )
        self.target_order_item.recycle_tax_value = (
            self.source_order_item.recycle_tax_value
        )
        self.target_order_item.save()
        self.items_changed_at = timezone.now()
        self.save(update_fields=['items_changed_at'])

    def _update_for_commit_recalculations(self, options: dict) -> (str, str):
        if options['should_add_assembly']:
            self.add_assembly(self.target_order_item)
        if options['without_assembly_price_change']:
            self._copy_assembly_price_from_source(options['should_add_assembly'])
        if options['without_shelf_price_change']:
            self._copy_order_item_price_from_source()

        if should_recalculate_order_again(options):
            calculator = OrderPriceCalculatorForPriceUpdatedAtPricing(self)
            calculator.calculate(recalculate_items=False)
            return calculator.message_status, calculator.message_text
        return '', ''

    def add_assembly(self, order_item: Optional['OrderItem'] = None) -> None:
        from orders.internal_api.events import OrderAssemblyChangedEvent

        self.assembly = True
        self.order_notes = get_order_note(
            assembly_type=AssemblyTypeChoices.ASSEMBLY_PAID,
            current_note=self.order_notes,
        )
        logistic_order = self.get_logistic_order_by_item_or_first(order_item)

        if logistic_order:
            OrderAssemblyChangedEvent(self, order_item, logistic_order)
        for product in self.product_set.all():
            product.priority_updater.set_assembly_delivery_priority()
        self.items_changed_at = timezone.now()
        self.save(update_fields=['order_notes', 'items_changed_at', 'assembly'])

    def remove_assembly(self, order_item: Optional['OrderItem'] = None) -> None:
        from orders.internal_api.events import OrderAssemblyChangedEvent

        self.assembly = False
        self.order_notes = replace_assembly_type_order_note(
            self,
            assembly_type_old=ASSEMBLY_TYPE_TO_NOTE[AssemblyTypeChoices.ASSEMBLY_PAID],
            assembly_type_new='',
        )
        logistic_order = self.get_logistic_order_by_item_or_first(order_item)

        if logistic_order:
            OrderAssemblyChangedEvent(self, order_item, logistic_order)
        for product in self.product_set.all():
            product.priority_updater.set_normal_delivery_priority()
        self.items_changed_at = timezone.now()
        self.save(update_fields=['order_notes', 'items_changed_at', 'assembly'])

    def get_logistic_order_by_item_or_first(
        self,
        order_item: Optional['OrderItem'] = None,
    ) -> LogisticOrderDTO | None:
        if order_item:
            return order_item.get_logistic_order()
        return self.logistic_info[0]

    def clear_switch_status_related(self) -> None:
        self.source_order_item = None
        self.target_order_item = None
        self.source_total_price = None
        self.source_total_price_net = None
        self.source_region_total_price = None
        self.source_region_total_price_net = None

    def _delete_correction_request(self):
        correction_request = CSCorrectionRequest.objects.get(
            invoice__order=self,
            status=CSCorrectionRequestStatus.STATUS_NEW,
            type_cs=CSCorrectionRequestType.TYPE_SWITCH,
        )
        correction_request.correction_invoice.delete()
        correction_request.delete()

    def _delete_latest_proforma(self):
        latest_proforma = (
            Invoice.objects.filter(order=self, status=InvoiceStatus.PROFORMA)
            .order_by('-id')
            .first()
        )
        latest_proforma.delete()

    @transition(
        field=switch_status,
        source='*',
        target=SwitchStatus.BLANK.value,
        on_error=SwitchStatus.BLANK.value,
        custom=dict(admin=False),
    )
    def reset_switch_status_to_blank(self) -> None:
        """Reset field status for Complaints"""

    @transaction.atomic
    @fsm_log_by
    @transition(
        field=switch_status,
        source=[
            SwitchStatus.BLANK.value,
            SwitchStatus.COMPLETED.value,
            SwitchStatus.CANCELLED.value,
        ],
        target=SwitchStatus.ITEM_REPLACEMENT.value,
        on_error=SwitchStatus.BLANK.value,
        custom=dict(admin=False),
    )
    def commit_item_on_hold(self, order_item: 'OrderItem', by: 'User') -> None:
        if not can_order_item_be_switched(order_item):
            raise OrderItemAlreadyBatched
        source_product = order_item.product_set.first()
        source_product.priority_updater.change_priority(
            ProductPriority.ON_HOLD, owner=by
        )
        self.source_order_item = order_item

    @transaction.atomic
    @fsm_log_by
    @transition(
        field=switch_status,
        source=SwitchStatus.ITEM_REPLACEMENT.value,
        target=SwitchStatus.CANCELLED.value,
        on_error=SwitchStatus.ITEM_REPLACEMENT.value,
        custom=dict(admin=False),
    )
    def rollback_item_on_hold(
        self, by: 'User', product_priority: ProductPriority
    ) -> None:
        source_product = self.source_order_item.product_set.first()
        source_product.priority_updater.change_priority(product_priority, owner=by)
        self.source_order_item = None

    @transaction.atomic
    @fsm_log_by
    @transition(
        field=switch_status,
        source=SwitchStatus.ITEM_REPLACEMENT.value,
        target=SwitchStatus.COST_RECALCULATION.value,
        on_error=SwitchStatus.ITEM_REPLACEMENT.value,
        custom=dict(admin=False),
    )
    def commit_item_replacement(
        self, furniture_to_add: 'FurnitureAbstract', quantity: int, by: 'User'
    ) -> (str, str):
        self._delete_order_item(self.source_order_item)

        target_order_item = self._create_and_add_target_order_item(
            furniture_to_add, quantity
        )
        self.target_order_item = target_order_item

        self.source_total_price = self.total_price
        self.source_total_price_net = self.total_price_net
        self.source_region_total_price = self.region_total_price
        self.source_region_total_price_net = self.region_total_price_net
        calculator = OrderPriceCalculatorForPriceUpdatedAtPricing(self)
        calculator.calculate(recalculate_items=False)
        return calculator.message_status, calculator.message_text

    @transaction.atomic
    @fsm_log_by
    @transition(
        field=switch_status,
        source=SwitchStatus.COST_RECALCULATION.value,
        target=SwitchStatus.ITEM_REPLACEMENT.value,
        on_error=SwitchStatus.COST_RECALCULATION.value,
        custom=dict(admin=False),
    )
    def rollback_item_replacement(self, by: 'User') -> (str, str):
        self._delete_order_item(self.target_order_item)
        self.target_order_item = None

        self._undelete_order_item(self.source_order_item)

        self.source_total_price = None
        self.source_total_price_net = None
        self.source_region_total_price = None
        self.source_region_total_price_net = None
        calculator = OrderPriceCalculatorForPriceUpdatedAtPricing(self)
        calculator.calculate(recalculate_items=False)
        return calculator.message_status, calculator.message_text

    @transaction.atomic
    @fsm_log_by
    @fsm_log_description
    @transition(
        field=switch_status,
        source=SwitchStatus.COST_RECALCULATION.value,
        target=RETURN_VALUE(
            SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value,
            SwitchStatus.COMPLETED.value,
        ),
        on_error=SwitchStatus.COST_RECALCULATION.value,
        custom=dict(admin=False),
    )
    def commit_recalculations(self, options: dict, by: 'User', description: str) -> str:
        from orders.internal_api.events import OrderRefreshEvent
        from orders.services.process_to_production import ProcessOrderToProduction

        self.switch_options = options
        message_status, message_text = self._update_for_commit_recalculations(options)
        self.message_status = message_status
        self.message_text = message_text

        if self.should_process_order_item_to_production():
            order_type = self.target_order_item.get_furniture_type()
            logistic_order = self.get_logistic_order_by_order_type(order_type)
            CreateProductFromOrderItem(self.target_order_item).create(logistic_order.id)
            if self.is_france() and not self.target_order_item.is_samplebox():
                ProcessOrderToProduction.update_recycle_tax_value(
                    self.target_order_item
                )

            OrderRefreshEvent(self)
            furniture_item = self.target_order_item.order_item
            add_jetty_state_to_redis(
                user_id=self.owner.id,
                jetty=furniture_item,
                source=ShelfStatusSource.TRANSACTION,
                pagepath='missing refferer',
            )
            self.estimated_delivery_time = self.get_estimated_delivery_time()

        if self.is_klarna_payment():
            self.create_proforma_invoice()
        elif not self.is_free():
            with InvoiceCorrector(self) as corrector:
                corrector.create_correction_request(options, by)

        self.total_price_change = self.total_price_after_switch_diff.brutto
        if self.total_price_change <= Decimal('0.0'):
            self.completed_target_order_items.add(self.target_order_item)
            source_product = self.source_order_item.product_set.first()
            source_product.delete()
            ProductDeletedEvent(source_product)
            return SwitchStatus.COMPLETED.value

        target_product = self.target_order_item.product_set.first()
        target_product.priority_updater.change_priority(
            ProductPriority.ON_HOLD, owner=by
        )
        return SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value

    @transaction.atomic
    @fsm_log_by
    @fsm_log_description
    @transition(
        field=switch_status,
        source=[
            SwitchStatus.COMPLETED.value,
            SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value,
        ],
        target=SwitchStatus.COST_RECALCULATION.value,
        on_error=SwitchStatus.COMPLETED.value,
        custom=dict(admin=False),
    )
    def rollback_recalculations(
        self, options: dict, by: 'User', description: str
    ) -> str:
        from orders.internal_api.events import OrderRefreshEvent

        if options['should_add_assembly']:
            self.remove_assembly(self.source_order_item)

        if self.should_process_order_item_to_production():
            target_product = self.target_order_item.product_set.first()
            target_product.delete()
            ProductDeletedEvent(target_product)

            OrderRefreshEvent(self)
            self.estimated_delivery_time = self.get_estimated_delivery_time()

        if self.is_klarna_payment():
            self._delete_latest_proforma()
        elif not self.is_free():
            self._delete_correction_request()

    @transaction.atomic
    @fsm_log_by
    @transition(
        field=switch_status,
        source=SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value,
        target=SwitchStatus.COMPLETED.value,
        on_error=SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value,
        custom=dict(admin=False),
    )
    def commit_additional_payment(self, by: 'User') -> 'Product':
        self.completed_target_order_items.add(self.target_order_item)
        source_product = self.source_order_item.product_set.first()
        source_product.delete()
        ProductDeletedEvent(source_product)

        target_product = self.target_order_item.product_set.first()
        target_product.priority_updater.change_priority(
            ProductPriority.NORMAL, owner=by
        )
        target_product.set_priority_from_order()
        return target_product

    @transaction.atomic
    @fsm_log_by
    @transition(
        field=switch_status,
        source=SwitchStatus.COMPLETED.value,
        target=SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value,
        on_error=SwitchStatus.COMPLETED.value,
        custom=dict(admin=False),
    )
    def rollback_additional_payment(self, by: 'User') -> 'Product':
        self.completed_target_order_items.remove(self.target_order_item)
        order_type = self.source_order_item.get_furniture_type()
        logistic_order = self.get_logistic_order_by_order_type(order_type)
        source_product = self.source_order_item.product_set.deleted_only().first()
        source_product.undelete()
        source_product.logistic_order = logistic_order.id
        source_product.save()
        ProductCreatedEvent(source_product)
        return source_product


class OrderSplitSampleAndFurnitureMixin(models.Model):
    class Meta:
        abstract = True

    def is_split(self) -> bool:
        return self.is_suborder_for_split() or self.is_parent_for_split()

    def is_parent_for_split(self) -> bool:
        return (
            self.suborders.first() is not None
            and self.suborders.first().is_united_kingdom_with_samples_only()
        )

    def is_suborder_for_split(self) -> bool:
        return (
            self.parent_order is not None and self.is_united_kingdom_with_samples_only()
        )

    def check_if_voucher_applies_for_split(self) -> bool:
        if self.parent_order is not None:
            used_promo = self.parent_order.used_promo
            return used_promo is not None and used_promo.is_percentage()
        return self.is_parent_for_split() and self.used_promo

    def should_split_order_by_samples_and_furniture(self) -> bool:
        return self.is_united_kingdom() and self.contains_samples_and_furniture

    @property
    def contains_samples_and_furniture(self) -> bool:
        if not self.items.exists():
            return False
        content_type_models = set(
            self.items.values_list('content_type__model', flat=True)
        )
        return 'samplebox' in content_type_models and len(content_type_models) > 1

    def split_orders_for_sample_and_furniture(self) -> 'Order':
        sample_order_cart = self._create_order_for_sample_boxes()
        self._move_sample_boxes_to_sample_order(sample_order_cart)
        sample_order_cart.change_status(OrderStatus.PAYMENT_PENDING)
        sample_order_cart.change_products_to_ordered()
        if not sample_order_cart.order_pretty_id:
            sample_order_cart.create_pretty_id()

        calculator = OrderPriceCalculator(sample_order_cart)
        calculator.calculate()
        return sample_order_cart

    def _create_order_for_sample_boxes(self) -> 'Order':
        sample_order_cart = deepcopy(self)
        sample_order_cart.pk = None
        sample_order_cart.parent_order = self
        sample_order_cart.order_pretty_id = self.pk
        sample_order_cart.region_vat = True
        if not sample_order_cart.check_if_voucher_applies_for_split():
            sample_order_cart.clear_promo(commit=False)
        sample_order_cart.save()
        return sample_order_cart

    def _move_sample_boxes_to_sample_order(self, sample_order_cart: 'Order') -> None:
        sample_items = self.items.filter(content_type__model='samplebox')
        for sample_item in sample_items:
            sample_item.order = sample_order_cart
            sample_item.save()

        if not sample_order_cart.check_if_voucher_applies_for_split():
            sample_order_cart.items.update(region_promo_value=Decimal('0.0'))
            sample_order_cart.items.update(region_delivery_promo_value=Decimal('0.0'))


@enum.unique
class ExtraDiscountStatus(ChoicesMixin, enum.Enum):
    BLANK = 'blank'
    DISCOUNT_RECALCULATION = 'discount_recalculation'
    COMPLETED = 'completed'
    CANCELLED = 'cancelled'


class OrderAddExtraDiscountMixin(models.Model):
    extra_discount_status = FSMField(
        default=ExtraDiscountStatus.BLANK.value,
        choices=ExtraDiscountStatus.choices(),
        db_index=True,
        protected=True,
    )

    completed_used_promos = SortedManyToManyField(
        'vouchers.Voucher', blank=True, related_name='completed_orders'
    )
    source_extra_discount_total_price = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )
    source_extra_discount_total_price_net = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )
    source_extra_discount_region_total_price = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )
    source_extra_discount_region_total_price_net = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )

    class Meta:
        abstract = True

    def is_extra_discount_in_progress(self) -> bool:
        return (
            self.extra_discount_status
            == ExtraDiscountStatus.DISCOUNT_RECALCULATION.value
        )

    @property
    def total_price_difference(self) -> Price:
        brutto = self.source_extra_discount_total_price - self.total_price
        netto = self.source_extra_discount_total_price_net - self.total_price_net
        return Price(brutto, netto)

    @property
    def region_total_price_difference(self) -> Price:
        brutto = self.source_extra_discount_region_total_price - self.region_total_price
        netto = (
            self.source_extra_discount_region_total_price_net
            - self.region_total_price_net
        )
        return Price(brutto, netto)

    def voucher_value_difference(self) -> float:
        previous_voucher = self.completed_used_promos.last()
        if previous_voucher is None:
            return self.used_promo.value
        return self.used_promo.value - previous_voucher.value

    def voucher_value_difference_by_furniture_item(
        self, furniture_item: 'FurnitureAbstract'
    ) -> float:
        previous_voucher = self.completed_used_promos.last()
        if previous_voucher is None:
            return self.used_promo.get_discount_value_for_item(furniture_item)
        current_value = self.used_promo.get_discount_value_for_item(furniture_item)
        previous_value = previous_voucher.get_discount_value_for_item(furniture_item)
        return current_value - previous_value

    def set_used_promo_with_history(self, target_voucher: 'Voucher') -> None:
        if self.used_promo:
            self.completed_used_promos.add(self.used_promo)
        self.set_used_promo(target_voucher)

    def set_used_promo(self, target_voucher: Optional['Voucher']) -> None:
        if target_voucher is None:
            self.used_promo = None
            self.clear_promo_amounts(save=True)
            self.items.update(region_promo_value=Decimal('0.0'))
            self.items.update(region_delivery_promo_value=Decimal('0.0'))
        else:
            self.used_promo = target_voucher
            self.save(update_fields=['used_promo'])

    def display_value_with_kind_of(self) -> str:
        used_promo = self.used_promo
        if used_promo.is_absolute():
            return f'{used_promo.value}€'
        by_furniture_items = [
            (
                f'{used_promo.get_discount_value_for_item(order_item.order_item)}%'
                f': {order_item.order_item}'
            )
            for order_item in self.items.all()
        ]
        return ', '.join(by_furniture_items)

    def create_klarna_adjustment(self) -> 'KlarnaAdjustment':
        from customer_service.forms import KlarnaAdjustmentCreateForm

        proforma = (
            self.invoice_set.filter(status=InvoiceStatus.PROFORMA)
            .order_by('-id')
            .first()
        )
        current_price = KlarnaAdjustmentCreateForm.get_current_price(proforma.id)
        reason = self.display_value_with_kind_of()
        klarna_adjustment = KlarnaAdjustment.objects.create(
            invoice=proforma,
            change_type=KlarnaPriceChangeType.DISCOUNT,
            amount=self.region_total_price_difference.brutto,
            current_price=current_price,
            source=KlarnaSource.ADD_EXTRA_DISCOUNT,
            reason=f'Add extra discount - {reason}',
        )
        return klarna_adjustment

    def create_correction_request(self, user: 'User') -> 'CSCorrectionRequest':
        source_invoice = (
            self.invoice_set.exclude(
                status__in=[InvoiceStatus.PROFORMA, InvoiceStatus.CORRECTING_DRAFT]
            )
            .order_by('-issued_at')
            .first()
        )

        correction_request = CSCorrectionRequest(
            issuer=user,
            correction_context='Add Extra Discount',
            type_cs=CSCorrectionRequestType.TYPE_EXTRA_DISCOUNT,
            invoice=source_invoice,
            correction_amount_gross=self.region_total_price_difference.brutto,
            tag=InvoiceItemTag.DISCOUNT_PRICE_ADJUSTMENT_DUE_TO_ANY_OTHER_REASON.value,
        )
        strategy = CorrectionRequestAmountStrategy(correction_request)
        correction_request = strategy.prepare_correction_request()
        return correction_request

    @transition(
        field=extra_discount_status,
        source='*',
        target=ExtraDiscountStatus.BLANK.value,
        on_error=ExtraDiscountStatus.BLANK.value,
        custom=dict(admin=False),
    )
    def reset_extra_discount_status_to_blank(self) -> None:
        """Reset field status for Complaints"""

    @transaction.atomic
    @fsm_log_by
    @transition(
        field=extra_discount_status,
        source=[
            ExtraDiscountStatus.BLANK.value,
            ExtraDiscountStatus.COMPLETED.value,
            ExtraDiscountStatus.CANCELLED.value,
        ],
        target=ExtraDiscountStatus.DISCOUNT_RECALCULATION.value,
        on_error=ExtraDiscountStatus.BLANK.value,
        custom=dict(admin=False),
    )
    def commit_discount_recalculation(
        self, target_voucher: 'Voucher', by: 'User'
    ) -> (str, str):
        self.source_extra_discount_total_price = self.total_price
        self.source_extra_discount_total_price_net = self.total_price_net
        self.source_extra_discount_region_total_price = self.region_total_price
        self.source_extra_discount_region_total_price_net = self.region_total_price_net

        self.set_used_promo_with_history(target_voucher)

        calculator = OrderPriceCalculatorForPriceUpdatedAtPricing(self)
        calculator.calculate(change_promo_quantity=True, recalculate_items=False)

        return calculator.message_status, calculator.message_text

    @transaction.atomic
    @fsm_log_by
    @transition(
        field=extra_discount_status,
        source=ExtraDiscountStatus.DISCOUNT_RECALCULATION.value,
        target=ExtraDiscountStatus.CANCELLED.value,
        on_error=ExtraDiscountStatus.CANCELLED.value,
        custom=dict(admin=False),
    )
    def rollback_discount_recalculation(self, by: 'User') -> (str, str):
        self.source_extra_discount_total_price = None
        self.source_extra_discount_total_price_net = None
        self.source_extra_discount_region_total_price = None
        self.source_extra_discount_region_total_price_net = None

        target_voucher = self.used_promo
        target_voucher.quantity_left += 1
        target_voucher.save(update_fields=['quantity_left'])

        previous_voucher = self.completed_used_promos.last()
        if previous_voucher:
            self.completed_used_promos.remove(previous_voucher)

        self.set_used_promo(previous_voucher)

        calculator = OrderPriceCalculatorForPriceUpdatedAtPricing(self)
        calculator.calculate(recalculate_items=False)

        return calculator.message_status, calculator.message_text

    @transaction.atomic
    @fsm_log_by
    @transition(
        field=extra_discount_status,
        source=ExtraDiscountStatus.DISCOUNT_RECALCULATION.value,
        target=ExtraDiscountStatus.COMPLETED.value,
        on_error=ExtraDiscountStatus.DISCOUNT_RECALCULATION.value,
        custom=dict(admin=False),
    )
    def commit_completed(self, by: 'User') -> CSCorrectionRequest | KlarnaAdjustment:
        if self.is_klarna_with_proforma_only():
            return self.create_klarna_adjustment()
        return self.create_correction_request(by)

    def is_klarna_with_proforma_only(self) -> bool:
        return (
            self.is_klarna_payment()
            and not self.invoice_set.filter(status=InvoiceStatus.ENABLED).exists()
        )
