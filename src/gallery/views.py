import base64
import logging
import os
import string

from collections import defaultdict
from copy import deepcopy
from typing import Dict

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db.models import (
    CharField,
    Value,
)
from django.db.transaction import atomic
from django.utils import translation
from django.utils.functional import cached_property
from django.utils.translation import get_language

from PIL import Image
from requests.exceptions import HTTPError
from rest_framework import (
    status,
    viewsets,
)
from rest_framework.authentication import (
    SessionAuthentication,
    TokenAuthentication,
)
from rest_framework.decorators import action
from rest_framework.generics import (
    GenericAPIView,
    ListAPIView,
    RetrieveAPIView,
    get_object_or_404,
)
from rest_framework.permissions import (
    AllowAny,
    IsAdminUser,
)
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.views import APIView

from carts.exceptions import MaxCartSizeError
from carts.services.cart_service import CartService
from catalogue.models import CatalogueEntry
from custom.enums import (
    Furniture,
    ShelfType,
)
from custom.metrics import metrics_client
from custom.shelf_states_interactor import add_jetty_state_to_redis
from custom.utils.python2_specific import HeavyThrottle
from custom.utils.url import get_request_source
from events.choices import (
    BrazeSavedItemSubscriptionSources,
    BrazeSubscriptionStates,
)
from events.domain_events.marketing_events import (
    CartUpdateEvent,
    WishlistAddEvent,
    WishlistEmptyEvent,
)
from events.tasks import subscribe_email_event
from gallery.enums import (
    CapeCollectionType,
    ConfiguratorTypeEnum,
    FurnitureImageType,
    FurnitureStatusEnum,
    ShelfStatusSource,
)
from gallery.exceptions import UnsupportedModel
from gallery.models import (
    CustomDna,
    FurnitureImage,
    Jetty,
    SampleBox,
    Sotty,
    Watty,
)
from gallery.models.furniture_abstract import FurnitureAbstract
from gallery.paginator import WishlistPagination
from gallery.permissions import WriteOnlyToNotPresetAndNotReadonly
from gallery.serializers import (
    FurnitureGridImageSerializer,
    FurnitureImageSerializer,
    JettySerializer,
    SampleBoxSerializer,
    SottySerializer,
    WattySerializer,
)
from gallery.serializers.furniture.influencers import (
    JettyInfluencerSerializer,
    WattyInfluencerSerializer,
)
from gallery.serializers.furniture.wishlist import (
    FurnitureWishlistSerializer,
    JettyWishlistDetailSerializer,
    SottyWishlistDetailSerializer,
    WattyWishlistDetailSerializer,
)
from gallery.services.add_to_wishlist import WishlistService
from gallery.services.bag import send_preset_to_bag
from gallery.services.copy_furniture import copy_furniture
from gallery.services.prices_for_serializers import get_currency_rate
from gallery.services.saved_item_events import emit_saved_item_related_events
from gallery.slugs import get_slug_for_furniture
from gallery.types import FurnitureType
from gallery.utils import (
    convert_image_to_file,
    fill_transparent_background,
    get_request_platform,
    send_to_facebook_scraper,
)
from pricing_v3.omnibus import OmnibusCalculator
from promotions.utils import strikethrough_promo
from regions.cached_region import (
    CachedRegionData,
    get_region_data_from_request,
)
from regions.mixins import RegionCalculationsObject
from regions.models import Region
from user_profile.choices import SubscriptionSources
from user_profile.decorators import create_and_login_user
from user_profile.models import UserProspect
from waiting_list.models import WaitingListEntry

logger = logging.getLogger('cstm')

User = get_user_model()


class FurnitureImageRenderListView(ListAPIView):
    serializer_class = FurnitureImageSerializer

    def get_queryset(self):
        render_types = [
            FurnitureImageType.RENDER,
            FurnitureImageType.RENDER_SMALL,
            FurnitureImageType.HOVER_RENDER,
            FurnitureImageType.HOVER_RENDER_SMALL,
        ]
        return FurnitureImage.objects.filter(type__in=render_types)


class FurnitureViewSet(viewsets.ModelViewSet):
    permission_classes = (AllowAny,)
    alphabet = string.ascii_lowercase + string.digits
    throttle_classes = ()  # temporary
    authentication_classes = [
        *api_settings.DEFAULT_AUTHENTICATION_CLASSES,
        TokenAuthentication,
    ]

    def get_queryset(self):
        if self.request.user.is_superuser:
            return super(FurnitureViewSet, self).get_queryset()
        else:
            return (
                super(FurnitureViewSet, self)
                .get_queryset()
                .filter_public_and_private_for(owner_id=self.request.user.id)
            )

    def get_serializer_context(self):
        if self.request.user.is_authenticated:
            cached_region_data = self.request.user.profile.cached_region_data
        else:
            cached_region_data = get_region_data_from_request(self.request)

        return {
            'region': cached_region_data,
            'currency_rate': get_currency_rate(cached_region_data),
            'strikethrough_promotion': strikethrough_promo(cached_region_data),
            **super().get_serializer_context(),
        }

    def get_data_from_request_data(self):
        request_data = deepcopy(self.request.data)
        data = request_data.pop('geom', {})
        data.update(request_data)
        if data.get('configurator_type', ConfiguratorTypeEnum.ROW) in {
            ConfiguratorTypeEnum.COLUMN,
            ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
        }:
            dna_object = data['configurator_params']['geom_id']
            data.update({'dna_object': dna_object})
        return data

    @create_and_login_user()
    def create(self, request, *args, **kwargs):
        data = self.get_data_from_request_data()
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )

    def perform_create(
        self,
        serializer,
        furniture_status: FurnitureStatusEnum = None,
    ) -> FurnitureAbstract:
        cached_region_data = self.request.user.profile.cached_region_data
        item = serializer.save(owner=self.request.user)

        # TODO: cached_region_data or profile.get_region() ?
        item.price = item.get_shelf_price_as_number(region=cached_region_data)
        item.created_platform = get_request_platform(self.request)
        fields_to_update = ['price', 'created_platform']

        if furniture_status:
            item.furniture_status = furniture_status
            fields_to_update.append('furniture_status')

        item.save(update_fields=fields_to_update)

        magic_preview = self.request.data.get('magic_preview', None)
        item.set_preview(magic_preview, 'webgl_preview')
        self.request.user.profile.clear_library_item_number_cache()

        return item

    def perform_update(self, serializer):
        item = serializer.save()
        if 'magic_preview' in self.request.data:
            item.set_preview(self.request.data['magic_preview'], 'webgl_library')

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response({'detail': 'success'}, status=status.HTTP_200_OK)

    def perform_destroy(self, instance):
        if (
            instance.owner == self.request.user
            and instance.furniture_status == FurnitureStatusEnum.DRAFT
        ):
            if cart := CartService.get_cart(self.request.user):
                cart_service = CartService(cart=cart)
                content_type = ContentType.objects.get_for_model(instance.__class__)
                cart_service.delete_from_cart(instance.id, content_type)
        else:
            self._handle_empty_wishlist(instance)
            instance.deleted = True
            with atomic():
                instance.save()
        self.request.user.profile.clear_library_item_number_cache()

    @create_and_login_user()
    @action(
        methods=['post'],
        detail=False,
        url_path='add_to_wishlist',
        url_name='add-by-geom-to-wishlist',
    )
    def add_to_wishlist_by_geom(self, request):
        """Creates new furniture from given geometry for registered user"""

        data = self.get_data_from_request_data()
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        furniture = self.perform_create(
            serializer,
            furniture_status=FurnitureStatusEnum.SAVED,
        )

        try:
            self._send_wishlist_events(furniture)
        except HTTPError as e:
            logger.error(
                'Error while sending events for item %s: %s',
                furniture.id,
                e.response.text,
            )

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=self.get_success_headers(serializer.data),
        )

    @create_and_login_user()
    @action(
        methods=['post'],
        detail=True,
        url_path='add_to_wishlist',
        url_name='add-by-id-to-wishlist',
    )
    def add_to_wishlist_by_id(self, request, pk=None):
        """Copy and create new furniture for given ID for registered user"""
        furniture = self.get_object()
        new_furniture = copy_furniture(
            old_furniture=furniture,
            owner=self.request.user,
            new_furniture_status=FurnitureStatusEnum.SAVED,
            request=request,
        )
        self.request.user.profile.clear_library_item_number_cache()

        try:
            self._send_wishlist_events(new_furniture)
        except HTTPError as e:
            logger.error(
                'Error while sending events '
                f'for item {new_furniture.id}: {e.response.text}'
            )
        return Response(
            data={'created_furniture_id': new_furniture.id},
            status=status.HTTP_201_CREATED,
        )

    def _send_wishlist_events(self, furniture: FurnitureAbstract) -> None:
        WishlistAddEvent(
            user=self.request.user,
            user_id=self.request.user.id,
            furniture_id=furniture.id,
            furniture_type=furniture.furniture_type,
        )
        subscribe_email_event.delay(
            furniture.owner_id,
            email=self.request.user.email,
            subscription_data={
                'group': settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS['save_for_later'],
                'group_state': BrazeSubscriptionStates.SUBSCRIBED,
                'email': self.request.user.email,
                'source': BrazeSavedItemSubscriptionSources.WISHLIST,
            },
        )
        if isinstance(furniture, Jetty):
            add_jetty_state_to_redis(
                user_id=self.request.user.id,
                jetty=furniture,
                source=ShelfStatusSource.SAVE_FOR_LATER,
                pagepath=self.request.get_full_path(),
            )

        tags = [f'shelf_type:{furniture.shelf_type}']
        metrics_client().increment('api.save_to_library', 1, tags=tags)

    @create_and_login_user(register_referer=' save popup')
    @action(
        methods=['post'],
        permission_classes=[WriteOnlyToNotPresetAndNotReadonly],
        throttle_classes=[HeavyThrottle],
        detail=True,
        url_path='add_to_wishlist_popup',
        url_name='add-by-id-to-wishlist-popup',
    )
    def add_to_wishlist_popup_by_id(self, request, pk=None):
        """Copy and create new furniture for given ID for guest user"""
        data = deepcopy(self.request.data)
        email_address = data.get('email')
        popup_src = data.get('popup_src', '')
        has_marketing_permissions = data.pop('marketing_permission', False)
        if not email_address:
            return Response(
                {'details': 'Missing email'}, status=status.HTTP_400_BAD_REQUEST
            )

        furniture = self.get_object()
        new_furniture = copy_furniture(
            old_furniture=furniture,
            owner=self.request.user,
            new_furniture_status=FurnitureStatusEnum.SAVED,
            request=request,
        )
        self._handle_preview(item=new_furniture, data=data)

        service = WishlistService(
            request=request,
            email=email_address,
            marketing_perms=has_marketing_permissions,
            popup_src=popup_src,
        )
        service.add_to_wishlist_popup(item=new_furniture)
        user_exist = True if service.user_from_email else False
        data = {
            'created_furniture_id': new_furniture.id,
            'user_exist': user_exist,
            # TODO: Remove it, if possible
            'already_registered': user_exist,
        }

        return Response(data, status=status.HTTP_201_CREATED)

    def set_order_source(self, order):
        order.order_source = get_request_source(self.request)
        order.save(update_fields=['order_source'])

    @action(
        methods=['put'],
        permission_classes=[
            *api_settings.DEFAULT_PERMISSION_CLASSES,
            WriteOnlyToNotPresetAndNotReadonly,
        ],
        detail=True,
    )
    def update_catalogue_image(self, request, pk):
        furniture = self.get_object()
        catalogue_entry = CatalogueEntry.objects.get(
            object_id=furniture.id,
            content_type__model=furniture.furniture_type,
        )

        serializer = FurnitureGridImageSerializer(data=self.request.data, many=True)
        serializer.is_valid(raise_exception=True)

        # if images comes as list - search for proper material
        if len(serializer.validated_data) > 1:
            image_data = next(
                (
                    e
                    for e in serializer.validated_data
                    if e['color_slot'] == furniture.material
                ),
                None,
            )
            if image_data is None:
                return Response(status=status.HTTP_400_BAD_REQUEST)

        image_file = ContentFile(
            base64.b64decode(image_data['magic_preview']),
            f'i_{pk}_c_{image_data["color_slot"]}.png',
        )
        with Image.open(image_file) as result_image:

            catalogue_entry.image_alternative = SimpleUploadedFile(
                f'{furniture.id}_alt.jpg', b'', 'image/jpeg'
            )
            result_image = result_image.convert('RGB')
            result_image.save(catalogue_entry.image_alternative, format='JPEG')

            catalogue_entry.save()
        response_serializer = self.get_serializer(furniture)
        return Response(response_serializer.data)

    def get_validated_serializer(self, data, many: bool = False):
        serializer_class = self.get_serializer_class()
        serializer = serializer_class(
            data=data,
            many=many,
            context=self.get_serializer_context(),
        )
        serializer.is_valid(raise_exception=True)
        return serializer

    @create_and_login_user(register_referer='webgl addtocart')
    @action(
        methods=['post'],
        permission_classes=[
            WriteOnlyToNotPresetAndNotReadonly,
        ],
        detail=False,
    )
    def add_to_cart(self, request):
        user = request.user
        data = self.get_data_from_request_data()
        cached_region_data = user.profile.cached_region_data
        serializer = self.get_validated_serializer(data)
        item = serializer.save(
            owner=user,
            furniture_status=FurnitureStatusEnum.DRAFT,
        )

        item.price = item.get_shelf_price_as_number(region=cached_region_data)
        item.save(update_fields=['price'])

        if (
            not isinstance(item, SampleBox)
            and self.request.data.get('magic_preview', None) is not None
        ):
            image_with_background = fill_transparent_background(
                ContentFile(base64.b64decode(self.request.data['magic_preview']))
            )
            item.preview = convert_image_to_file(
                image_with_background,
                'webgl_cart.png',
                'PNG',
            )
            item.preview_webp = convert_image_to_file(
                image_with_background,
                'webgl_cart.webp',
                'WEBP',
            )
            item.save(update_fields=['preview', 'preview_webp'])

        cart = CartService.get_or_create_cart(user)
        try:
            CartService(cart).add_to_cart(item)
        except MaxCartSizeError:
            return Response(
                {'error': 'cart_max_size'}, status=status.HTTP_400_BAD_REQUEST
            )

        CartUpdateEvent(
            user=user,
            cart_id=cart.id,
            total_price=int(cart.total_price),
            is_sample_box=isinstance(item, SampleBox),
        )

        if isinstance(item, Jetty):
            metrics_client().increment(
                'api.save_to_cart',
                1,
                tags=[
                    'shelf_type:{}'.format(item.shelf_type),
                    'user_lang:{}'.format(user.profile.language),
                ],
            )
            add_jetty_state_to_redis(
                user_id=user.id,
                jetty=item,
                source=ShelfStatusSource.ADD_TO_CART,
                pagepath=self.request.get_full_path(),
            )

        del user.profile.status_response
        self.request.user.profile.clear_library_item_number_cache()
        return Response({'cart_id': cart.id}, status=status.HTTP_201_CREATED)

    @action(
        methods=['post'],
        permission_classes=[
            WriteOnlyToNotPresetAndNotReadonly,
            IsAdminUser,
        ],
        detail=False,
        throttle_classes=[],
    )
    def save_as_preset(self, request):
        user = request.user
        data = self.get_data_from_request_data()
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        item = serializer.save(
            owner=self.request.user, furniture_status=FurnitureStatusEnum.SPECIAL
        )
        item.price = item.get_shelf_price_as_number(region=user.profile.get_region())
        magic_preview = self.request.data['magic_preview']
        item.set_preview(magic_preview, 'webgl_preset.png')
        queryset = self.queryset.filter(id=item.id)
        send_preset_to_bag(queryset)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(
        methods=['post'],
        permission_classes=[
            WriteOnlyToNotPresetAndNotReadonly,
        ],
        detail=False,
    )
    def check_recycle_tax(self, request):
        # this is use only in France, so lets keep it simple
        data = self.get_data_from_request_data()
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        item = self.model(**serializer.validated_data)
        item.furniture_status = FurnitureStatusEnum.DRAFT
        item.created_platform = get_request_platform(request)
        return Response(
            {
                'recycle_tax_value': item.get_recycle_tax_value(),
            },
            status=status.HTTP_200_OK,
        )

    @create_and_login_user(register_referer=' save popup')
    @action(
        methods=['post'],
        throttle_classes=[
            HeavyThrottle,
        ],
        detail=True,
    )
    def share_on_social_media_popup(self, request, pk=None):
        data = self.get_data_from_request_data()
        serializer_class = self.get_serializer_class()
        serializer = serializer_class(data=data)
        serializer.is_valid(raise_exception=True)
        item = serializer.save(
            owner=self.request.user,
            furniture_status=FurnitureStatusEnum.SHARED,
            base_preset=pk,
        )

        magic_preview = self.request.data.get('magic_preview', None)
        item.set_preview(magic_preview, 'webgl_cart')

        send_to_facebook_scraper(request, item)
        return Response(
            {
                'id': item.id,
                'seo_slug': get_slug_for_furniture(item, get_language()),
                'furniture_category': item.furniture_category,
            },
            status=status.HTTP_200_OK,
        )

    @create_and_login_user(register_referer=' save popup')
    @action(
        methods=['post'],
        throttle_classes=[
            HeavyThrottle,
        ],
        detail=True,
    )
    def save_for_later_by_id_popup(self, request, pk=None):
        email_address = request.data.get('email')
        user = request.user
        user.email = email_address
        user.save(update_fields=['email'])

        profile = user.profile
        profile.email = email_address
        profile.save(update_fields=['email'])

        emit_saved_item_related_events(
            item=self.get_object(),
            email=email_address,
            source=SubscriptionSources.APP,
            marketing_permission=request.data.get('marketing_permission', False),
        )

        UserProspect.create_from_s4l(
            email=email_address, description=self.request.META.get('HTTP_X_REAL_IP')
        )

        return Response({'already_registered': False, 'status': 'ok'})

    @create_and_login_user(register_referer=' save popup')
    @action(
        methods=['post'],
        permission_classes=[WriteOnlyToNotPresetAndNotReadonly],
        throttle_classes=[HeavyThrottle],
        detail=False,
        url_path='add_to_wishlist_popup',
        url_name='add-by-geom-to-wishlist-popup',
    )
    def add_to_wishlist_popup_by_geom(self, request):
        """Creates new furniture from given geometry for guest user"""
        data = self.get_data_from_request_data()
        email_address = data.get('email')
        popup_src = data.get('popup_src', '')
        has_marketing_permissions = data.pop('marketing_permission', False)
        if not email_address:
            return Response(
                {'details': 'Missing email'}, status=status.HTTP_400_BAD_REQUEST
            )

        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        furniture = self.perform_create(
            serializer=serializer, furniture_status=FurnitureStatusEnum.SAVED
        )
        self._handle_preview(item=furniture, data=data)

        service = WishlistService(
            request=request,
            email=email_address,
            marketing_perms=has_marketing_permissions,
            popup_src=popup_src,
        )
        service.add_to_wishlist_popup(item=furniture)
        if service.user_from_email:
            response_status = status.HTTP_201_CREATED
            additional_data = {'user_exist': True, 'already_registered': True}
        else:
            response_status = status.HTTP_200_OK
            additional_data = {'user_exist': False, 'already_registered': False}
        serialized_data = serializer.data | additional_data

        return Response(serialized_data, status=response_status)

    # TODO: check if it's deprecated
    @staticmethod
    def _handle_preview(item: FurnitureType, data: dict) -> None:
        if magic_preview := data.get('magic_preview'):
            item.set_preview(magic_preview, 'webgl_cart')
            return

        old_item_id = data.get('id')
        if old_item_id and old_item_id != 1 and not magic_preview:
            try:
                old_item = type(item).objects.get(pk=old_item_id)
                item.preview = ContentFile(
                    old_item.preview.read(),
                    os.path.basename(old_item.preview.name),
                )
                item.save(update_fields=['preview'])
            except type(item).DoesNotExist:
                logger.debug('No item id=%s to copy the picture from' % old_item_id)
            except ValueError:
                logger.debug(
                    "Item's id=%s preview points to non-existent file" % old_item_id
                )

    # TODO: remove?
    @create_and_login_user(register_referer='waiting list')
    @action(
        methods=['post'],
        permission_classes=[
            WriteOnlyToNotPresetAndNotReadonly,
        ],
        detail=False,
    )
    def add_to_waiting_list(self, request):

        if self.model != Watty:
            raise UnsupportedModel

        data = self.get_data_from_request_data()
        serializer_class = self.get_serializer_class()
        serializer = serializer_class(
            data=data,
            context={'region': self.request.user.profile.cached_region_data},
        )
        serializer.is_valid(raise_exception=True)
        saved_item = serializer.save(
            owner=request.user,
            furniture_status=FurnitureStatusEnum.SPECIAL,
        )

        # todo: this should be rewritten to proper serializer

        waiting_entry = WaitingListEntry()
        waiting_entry.email_address = data.get('email')

        #  it may be missing or came as "", both falsy
        if waiting_entry.desired_time and 'value' in data.get('desiredTime', ''):
            waiting_entry.desired_time = data.get('desiredTime')['value']
        waiting_entry.watty = saved_item
        waiting_entry.owner = request.user
        waiting_entry.save()

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def _handle_empty_wishlist(self, instance: FurnitureAbstract) -> None:
        """Emit event if last item from wishlist is about to be deleted."""
        if (
            instance.owner == self.request.user
            and instance.furniture_status == FurnitureStatusEnum.SAVED
            and instance.owner.profile.get_library_item_number() == 1
        ):
            WishlistEmptyEvent(user=instance.owner, user_id=instance.owner.id)


class JettyViewSet(FurnitureViewSet):
    model = Jetty
    serializer_class = JettySerializer
    queryset = Jetty.objects.all()

    @create_and_login_user(register_referer=' save popup')
    @action(
        methods=['post'],
        permission_classes=[
            WriteOnlyToNotPresetAndNotReadonly,
        ],
        throttle_classes=[
            HeavyThrottle,
        ],
        detail=False,
    )
    def reserve_from_other_region(self, request):
        """Save contact details and configured jetty for users in other region."""
        data = self.get_data_from_request_data()
        serializer_class = self.get_serializer_class()
        serializer = serializer_class(data=data)
        if serializer.is_valid():
            item = serializer.save(
                owner=request.user,
                furniture_status=FurnitureStatusEnum.DRAFT,
            )
            magic_preview = data.get('magic_preview', None)
            item.set_preview(magic_preview, 'webgl_cart')
            if isinstance(item, Jetty):
                add_jetty_state_to_redis(
                    user_id=request.user.id,
                    jetty=item,
                    source=ShelfStatusSource.SAVE_FOR_LATER,
                    pagepath=self.request.get_full_path(),
                )
        return Response(serializer_class.data)


class SampleBoxViewSet(FurnitureViewSet):
    model = SampleBox
    queryset = SampleBox.objects.all()
    serializer_class = SampleBoxSerializer

    @create_and_login_user(register_referer='webgl addtocart')
    @action(
        methods=['post'],
        permission_classes=[
            WriteOnlyToNotPresetAndNotReadonly,
        ],
        detail=False,
    )
    def add_to_cart(self, request):
        # Unify with add_to_cart in FurnitureViewSet, refactor and remove duplication
        # https://cstm-tasks.atlassian.net/browse/QK-751
        user = request.user
        serializer = self.get_validated_serializer(
            request.data.get('items', []), many=True
        )
        samples = serializer.save(
            owner=user,
            furniture_status=FurnitureStatusEnum.DRAFT,
            created_platform=get_request_platform(self.request),
        )

        cart = CartService.get_or_create_cart(user)
        for sample in samples:
            try:
                CartService(cart).add_to_cart(sample, recalculate=False)
            except MaxCartSizeError:
                return Response(
                    {'error': 'cart_max_size'}, status=status.HTTP_400_BAD_REQUEST
                )
        CartService.recalculate_cart(cart)
        CartUpdateEvent(
            user=user,
            cart_id=cart.id,
            total_price=int(cart.total_price),
            is_sample_box=True,
        )

        del user.profile.status_response
        self.request.user.profile.clear_library_item_number_cache()
        return Response({'cart_id': cart.id}, status=status.HTTP_201_CREATED)

    def perform_create(self, serializer) -> SampleBox:
        cached_region_data = self.request.user.profile.cached_region_data
        item = serializer.save(owner=self.request.user)
        item.price = item.get_shelf_price_as_number(region=cached_region_data)
        item.created_platform = get_request_platform(self.request)
        item.save(update_fields=['price', 'created_platform'])

        self.request.user.profile.clear_library_item_number_cache()

        return item


class SottyViewSet(FurnitureViewSet):
    model = Sotty
    queryset = Sotty.objects.all()
    serializer_class = SottySerializer


class WattyViewSet(FurnitureViewSet):
    model = Watty
    queryset = Watty.objects.all()
    serializer_class = WattySerializer


class WishlistView(APIView):
    permission_classes = [AllowAny]
    pagination_class = WishlistPagination

    @staticmethod
    def _get_region(region: CachedRegionData) -> Region:
        return (
            Region.objects.select_related('currency')
            .prefetch_related('currency__rates')
            .get(id=region.id)
        )

    def get_serializer_context(self, region: CachedRegionData) -> Dict:
        db_region = self._get_region(region)
        strikethrough_promotion = strikethrough_promo(region=db_region)
        context = {
            'region_calculations_object': RegionCalculationsObject(db_region),
            'region': db_region,
            'currency_rate': db_region.currency.rates.first().rate,
            'request': self.request,
            'strikethrough_promotion': strikethrough_promotion,
        }
        if strikethrough_promotion:
            context['omnibus_calculator'] = OmnibusCalculator.get_instance(db_region)
        return context

    def get(self, request: Request, *args, **kwargs) -> Response:
        """Combine 3 querysets to one and avoid serializing all items, just the ones
        on the page.
        """
        if not request.user.is_authenticated:
            return self._get_empty_paginated_response()

        wishlist_items = self._get_combined_wishlist_items(user=request.user)

        paginator = self.pagination_class()
        page = paginator.paginate_queryset(wishlist_items, request)
        serialized_page = self._get_serialized_wishlist_page(page)
        return paginator.get_paginated_response(serialized_page)

    def _get_empty_paginated_response(self) -> Response:
        paginator = self.pagination_class()
        page = paginator.paginate_queryset([], self.request)
        return paginator.get_paginated_response(page)

    def _get_combined_wishlist_items(self, user: User) -> list[FurnitureAbstract]:
        """Create a union of jetties, watties, and sotties with only required fields."""

        minimal_fields = [
            'id',
            'created_at',
            'furniture_type',
        ]

        # Get base querysets with only minimal common fields
        jetties = (
            user.jetty_set.filter(furniture_status=FurnitureStatusEnum.SAVED)
            .annotate(
                furniture_type=Value(Furniture.jetty.value, output_field=CharField())
            )
            .values(*minimal_fields)
        )

        sotties = (
            user.sotty_set.filter(furniture_status=FurnitureStatusEnum.SAVED)
            .annotate(
                furniture_type=Value(Furniture.sotty.value, output_field=CharField())
            )
            .values(*minimal_fields)
        )

        watties = (
            user.watty_set.filter(furniture_status=FurnitureStatusEnum.SAVED)
            .annotate(
                furniture_type=Value(Furniture.watty.value, output_field=CharField())
            )
            .values(*minimal_fields)
        )

        qs = jetties.union(sotties, watties).order_by('-created_at')
        return qs

    def _get_furniture_ids_by_type(self, page: list[dict]) -> dict[str, list[int]]:
        """Group furniture IDs by their type."""
        furniture_ids_by_type = defaultdict(list)
        for item in page:
            furniture_type = item['furniture_type']
            furniture_ids_by_type[furniture_type].append(item['id'])
        return furniture_ids_by_type

    def _serialize_furniture_by_type(
        self,
        furniture_type: str,
        ids: list[int],
        context: dict,
    ) -> list[dict]:
        if not ids:
            return []
        objects = Furniture(furniture_type).model.objects.filter(id__in=ids)
        return FurnitureWishlistSerializer(objects, many=True, context=context).data

    def _get_serialized_wishlist_page(self, page: list[dict]) -> list[dict]:
        context = self.get_serializer_context(
            self.request.user.profile.cached_region_data
        )
        furniture_ids_by_type = self._get_furniture_ids_by_type(page)

        all_data = []
        for furniture_type, ids in furniture_ids_by_type.items():
            serialized_data = self._serialize_furniture_by_type(
                furniture_type,
                ids,
                context,
            )
            all_data.extend(serialized_data)

        all_data.sort(key=lambda x: x['created_at'], reverse=True)
        return all_data


class BaseWishlistDetailApiView(RetrieveAPIView):
    authentication_classes = [
        *api_settings.DEFAULT_AUTHENTICATION_CLASSES,
        TokenAuthentication,
    ]
    model = None

    def get_serializer_context(self) -> Dict:
        region = self.request.user.profile.cached_region_data
        return {
            'region': region,
            'currency_rate': get_currency_rate(region),
            'strikethrough_promotion': strikethrough_promo(region=region),
            **super().get_serializer_context(),
        }

    def get_object(self):
        return get_object_or_404(self.model, pk=self.kwargs['pk'])


class JettyWishlistDetailApiView(BaseWishlistDetailApiView):
    serializer_class = JettyWishlistDetailSerializer
    model = Jetty


class SottyWishlistDetailApiView(BaseWishlistDetailApiView):
    serializer_class = SottyWishlistDetailSerializer
    model = Sotty


class WattyWishlistDetailApiView(BaseWishlistDetailApiView):
    serializer_class = WattyWishlistDetailSerializer
    model = Watty


class CustomDnaPreviewView(RetrieveAPIView):
    """
    View for retrieving requested CustomDNA
    together with appropriate furniture instance.
    (used for internal needs only)
    """

    authentication_classes = [SessionAuthentication]

    def get_serializer_class(self):
        instance = self.get_object()
        return JettySerializer if isinstance(instance, Jetty) else WattySerializer

    def get_serializer_context(self):
        return {
            **super().get_serializer_context(),
            'region': get_region_data_from_request(self.request),
        }

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        serialized_furniture = serializer.data
        override_dna = self.custom_dna.get_details()
        if self.custom_dna.collection_type == CapeCollectionType.VINYL_STORAGE:
            serialized_furniture['shelf_category'] = 'vinyl_storage'

        serialized_furniture['shelf_type'] = self.custom_dna.shelf_type
        updated_data = {**serialized_furniture, 'override_dna_json': override_dna}
        return Response(updated_data)

    def get_object(self, queryset=None):
        return self.custom_dna.furniture_object

    @cached_property
    def custom_dna(self) -> CustomDna:
        return get_object_or_404(CustomDna, id=self.kwargs['pk'])


class BagPresetView(GenericAPIView):
    authentication_classes = [
        TokenAuthentication,
        *api_settings.DEFAULT_AUTHENTICATION_CLASSES,
    ]
    queryset = Jetty.objects.all()  # TODO: watty

    def get_serializer_class(self):
        shelf_type = self.request.data.get('shelf_type', ShelfType.TYPE01)
        furniture_type = ShelfType(shelf_type).furniture_type
        return {
            Furniture.jetty: JettySerializer,
            Furniture.watty: WattySerializer,
        }.get(furniture_type, JettySerializer)

    def post(self, request, *args, **kwargs):
        preview = request.data.pop('preview')
        grid_all_colors = request.data.pop('grid_all_colors')
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer, preview, grid_all_colors)
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
        )

    def perform_create(self, serializer, preview, grid_all_colors):
        region = self.request.user.profile.get_region()
        item = serializer.save(
            owner=self.request.user,
            furniture_status=FurnitureStatusEnum.SPECIAL,
            # TODO: which one is this?
            preset=True,
            preset_initial_state=True,
        )
        item.price = item.get_shelf_price_as_number(region=region)
        item.save()
        item.set_preview(preview, 'webgl_preset')
        item.set_grid_image(grid_all_colors, 'grid_all_colors_preset')


class InfluencersLPFurnitureListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        jetty_ids = self._get_ids_as_list('jetty_ids')
        watty_ids = self._get_ids_as_list('watty_ids')
        try:
            region = Region.objects.get(name=self.request.GET['region'])
        except KeyError:
            return Response(
                data='region parameter must be passed',
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Region.DoesNotExist:
            return Response(
                data='Wrong region name', status=status.HTTP_400_BAD_REQUEST
            )
        try:
            language = self.request.GET['lang']
        except KeyError:
            return Response(
                data='lang parameter must be passed', status=status.HTTP_400_BAD_REQUEST
            )

        jetties = Jetty.objects.filter(id__in=jetty_ids).prefetch_related(
            'additional_images'
        )
        watties = Watty.objects.filter(id__in=watty_ids).prefetch_related(
            'additional_images'
        )

        context = {
            'region': region,
            'rco': RegionCalculationsObject(region),
            'promotion': strikethrough_promo(region),
            'language': language,
        }
        with translation.override(language):
            jetty_data = JettyInfluencerSerializer(
                jetties, context=context, many=True
            ).data
            watty_data = WattyInfluencerSerializer(
                watties, context=context, many=True
            ).data

        return Response(jetty_data + watty_data)

    def _get_ids_as_list(self, param) -> list:
        ids = self.request.GET.get(param, '')
        if not ids:
            return []
        return [int(id_) for id_ in ids.split(',')]
