from django.urls import path

from gallery.ecommerce_api import (
    CustomDnaView,
    JettyConfiguratorView,
    MailingJettyAPIView,
    MailingSavedJettyAPIView,
    MailingSavedSottyAPIView,
    MailingSavedWattyAPIView,
    MailingSottyAPIView,
    MailingWattyAPIView,
    ProductPageJettyAPIView,
    ProductPageSottyAPIView,
    ProductPageSottySingleModuleAPIView,
    ProductPageWattyAPIView,
    SottyConfiguratorPreloadAPIView,
    SottyConfiguratorView,
    WattyConfiguratorView,
)

urlpatterns = [
    path(
        'jetty_configurator/<int:pk>/',
        JettyConfiguratorView.as_view(),
        name='jetty-configurator',
    ),
    path(
        'sotty_configurator/<int:pk>/',
        SottyConfiguratorView.as_view(),
        name='sotty-configurator',
    ),
    path(
        'watty_configurator/<int:pk>/',
        WattyConfiguratorView.as_view(),
        name='watty-configurator',
    ),
    path('furniture_dna/', CustomDnaView.as_view(), name='furniture-dna'),
    path(
        'jetty_pdp/<int:pk>/',
        ProductPageJettyAPIView.as_view(),
        name='jetty-pdp',
    ),
    path(
        'sotty_pdp/<int:pk>/',
        ProductPageSottyAPIView.as_view(),
        name='sotty-pdp',
    ),
    path(
        'sotty_pdp/single_modules/<int:pk>/',
        ProductPageSottySingleModuleAPIView.as_view(),
        name='sotty-single-module-pdp',
    ),
    path(
        'watty_pdp/<int:pk>/',
        ProductPageWattyAPIView.as_view(),
        name='watty-pdp',
    ),
    path(
        'sotty_configurator_preload/<int:pk>/',
        SottyConfiguratorPreloadAPIView.as_view(),
        name='sotty-preload',
    ),
    path(
        'jetty/<int:pk>/saved-item/',
        MailingSavedJettyAPIView.as_view(),
        name='jetty-mailing-saved',
    ),
    path(
        'sotty/<int:pk>/saved-item/',
        MailingSavedSottyAPIView.as_view(),
        name='sotty-mailing-saved',
    ),
    path(
        'watty/<int:pk>/saved-item/',
        MailingSavedWattyAPIView.as_view(),
        name='watty-mailing-saved',
    ),
    path(
        'jetty/<int:pk>/mailing-item/',
        MailingJettyAPIView.as_view(),
        name='jetty-mailing',
    ),
    path(
        'sotty/<int:pk>/mailing-item/',
        MailingSottyAPIView.as_view(),
        name='sotty-mailing',
    ),
    path(
        'watty/<int:pk>/mailing-item/',
        MailingWattyAPIView.as_view(),
        name='watty-mailing',
    ),
]
