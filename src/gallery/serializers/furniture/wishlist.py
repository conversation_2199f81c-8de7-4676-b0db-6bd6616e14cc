from decimal import Decimal
from typing import Optional

from django.utils.translation import get_language

from rest_framework import serializers

from gallery.enums import FurnitureCategory
from gallery.models import (
    <PERSON>y,
    So<PERSON>,
    <PERSON><PERSON>,
)
from gallery.services.prices_for_serializers import (
    RegionCurrencySerializerMixin,
    get_region_price_in_euro,
    get_region_price_with_discount,
    get_region_price_with_discount_in_euro,
)
from gallery.slugs import get_slug_for_furniture
from pricing_v3.omnibus import OmnibusCalculator
from promotions.models import Promotion


class BaseWishlistSerializer(
    RegionCurrencySerializerMixin,
    serializers.Serializer,
):
    id = serializers.IntegerField()
    category = serializers.CharField(source='furniture_category')
    region_price = serializers.SerializerMethodField()
    region_price_with_discount = serializers.SerializerMethodField()
    promotion = serializers.SerializerMethodField()
    omnibus_price = serializers.SerializerMethodField()

    @property
    def strikethrough_promotion(self) -> Optional[Promotion]:
        return self.context.get('strikethrough_promotion')

    @property
    def omnibus_calculator(self) -> OmnibusCalculator:
        omnibus_calculator = self.context.get('omnibus_calculator')
        return omnibus_calculator or OmnibusCalculator.get_instance(self.region)

    def get_region_price(self, obj) -> Decimal:
        return obj.get_regionalized_price(
            region=self.region,
            region_calculations_object=self.region_calculations_object,
        )

    def get_region_price_with_discount(self, obj) -> Decimal:
        return get_region_price_with_discount(
            furniture=obj,
            region=self.region,
            region_calculations_object=self.region_calculations_object,
            promotion=self.strikethrough_promotion,
        )

    def get_promotion(self, obj) -> float:
        return (
            self.strikethrough_promotion.promo_code.get_discount_value_for_item(obj)
            if self.strikethrough_promotion
            and self.strikethrough_promotion.promo_code.is_geometry_affected(obj)
            else 0.0
        )

    def get_omnibus_price(self, instance) -> Decimal | None:
        if not self.strikethrough_promotion:
            return
        return self.omnibus_calculator.calculate_lowest_price(
            geometry=instance,
            region_calculations_object=self.region_calculations_object,
        )


class FurnitureWishlistSerializer(
    BaseWishlistSerializer,
):
    region_price_in_euro = serializers.SerializerMethodField()
    region_price_with_discount_in_euro = serializers.SerializerMethodField()
    url = serializers.SerializerMethodField()
    title = serializers.ReadOnlyField(source='default_title')
    width = serializers.FloatField(source='get_width')
    height = serializers.FloatField(source='get_height')
    depth = serializers.FloatField(source='get_depth')
    preview = serializers.ImageField()
    shelf_type = serializers.IntegerField()
    configurator_type = serializers.IntegerField()
    created_at = serializers.DateTimeField()
    fabric = serializers.SerializerMethodField()

    @staticmethod
    def get_fabric(obj) -> str:
        return getattr(obj, 'fabric', '')

    def get_region_price_in_euro(self, obj) -> Decimal:
        return get_region_price_in_euro(
            furniture=obj,
            currency_rate=self.currency_rate,
            region=self.region,
            region_calculations_object=self.region_calculations_object,
        )

    def get_region_price_with_discount_in_euro(self, obj) -> Decimal:
        return get_region_price_with_discount_in_euro(
            furniture=obj,
            currency_rate=self.currency_rate,
            region=self.region,
            region_calculations_object=self.region_calculations_object,
            promotion=self.strikethrough_promotion,
        )

    def get_url(self, obj):
        uri = obj.get_url_with_region(region=self.region)
        # remove last /
        uri = uri[:-1] if uri.endswith('/') else uri
        if obj.shelf_category == FurnitureCategory.COVER:
            return uri
        return f'{uri}{get_slug_for_furniture(obj, get_language())}/'


class BaseWishlistDetailSerializer(BaseWishlistSerializer, serializers.ModelSerializer):
    class Meta:
        abstract = True
        fields = (
            'id',
            'category',
            'shelf_type',
            'preview',
            'omnibus_price',
            'promotion',
            'region_price',
            'region_price_with_discount',
        )


class JettyWishlistDetailSerializer(BaseWishlistDetailSerializer):
    class Meta(BaseWishlistDetailSerializer.Meta):
        model = Jetty


class SottyWishlistDetailSerializer(BaseWishlistDetailSerializer):
    class Meta(BaseWishlistDetailSerializer.Meta):
        model = Sotty


class WattyWishlistDetailSerializer(BaseWishlistDetailSerializer):
    class Meta(BaseWishlistDetailSerializer.Meta):
        model = Watty
