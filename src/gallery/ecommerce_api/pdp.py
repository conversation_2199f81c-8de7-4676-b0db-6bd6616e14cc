from django.core.cache import cache
from django.utils.translation import get_language

from rest_framework import status
from rest_framework.generics import RetrieveAPIView
from rest_framework.request import Request
from rest_framework.response import Response

from custom.enums import (
    ShelfType,
    Sofa01Color,
)
from custom.metrics import metrics_client
from custom.shelf_states_interactor import add_jetty_state_to_redis
from ecommerce_api.mixins import EcommerceRefererBasedMixin
from events.domain_events.marketing_events import FurnitureViewEvent
from gallery.ecommerce_api.mixins import ColorOverrideViewMixin
from gallery.enums import (
    FurnitureCategory,
    ShelfStatusSource,
)
from gallery.models import (
    Jetty,
    Sotty,
    Watty,
)
from gallery.serializers import (
    JettyPDPSerializer,
    WattyPDPSerializer,
)
from gallery.serializers.furniture.pdp import (
    SottyPDPSerializer,
    SottyPreloadSerializer,
    SottySingleModulePDPSerializer,
)
from gallery.services.prices_for_serializers import get_currency_rate
from promotions.utils import strikethrough_promo
from regions.mixins import RegionCalculationsObject
from regions.services.limitations import LimitationService
from reviews.queries import get_pdp_reviews
from reviews.serializers import GeneralReviewSerializer


class ProductPageBaseAPIView(
    ColorOverrideViewMixin,
    EcommerceRefererBasedMixin,
    RetrieveAPIView,
):
    model_class = None

    def get_queryset(self):
        return (
            self.model_class.objects.all()
            .select_related('owner')
            .prefetch_related('additional_images')
        )

    def get(self, request: Request, *args, **kwargs) -> Response:
        instance = self.get_object()
        self._trigger_events(instance)
        response_data = self._build_response_data(instance)
        return Response(response_data, status=status.HTTP_200_OK)

    def _build_response_data(self, instance) -> dict:
        serializer = self.get_serializer(instance)
        return serializer.data

    def _trigger_events(self, instance) -> None:
        self._trigger_analytics_events(instance)
        self._trigger_marketing_events(instance)

    def _trigger_analytics_events(self, instance) -> None:
        add_jetty_state_to_redis(
            user_id=self.request.user.id,
            jetty=instance,
            source=ShelfStatusSource.PRODUCT_VIEW,
            pagepath=self.request.META.get('HTTP_REFERER', 'missing referer'),
        )

        furniture_type = self.model_class.__name__.lower()
        agent_type = (
            'mobile'
            if self.request.user_agent.is_mobile or self.request.user_agent.is_tablet
            else 'web'
        )
        metrics_client().increment(
            'web.view.product',
            1,
            tags=[
                'agent_type:{}'.format(agent_type),
                'furniture_type:{}'.format(furniture_type),
            ],
        )

    def _trigger_marketing_events(self, instance) -> None:
        if self.request.user.is_authenticated:
            FurnitureViewEvent(
                user=self.request.user,
                last_viewed_furniture_id=instance.id,
                last_viewed_furniture_type=instance.furniture_type,
            )


class ProductDetailPageAPIView(ProductPageBaseAPIView):
    def _build_response_data(self, instance) -> dict:
        serializer = self.get_serializer(instance)
        reviews = self._get_reviews(instance.shelf_type, instance.furniture_category)
        return {**serializer.data, **reviews}

    @staticmethod
    def _get_reviews(
        shelf_type: ShelfType,
        furniture_category: FurnitureCategory,
    ) -> dict:
        language = get_language()
        cache_key = f'pdp_reviews_{language}_{shelf_type}_{furniture_category}'
        if reviews := cache.get(cache_key):
            return reviews

        queryset = get_pdp_reviews(shelf_type, furniture_category, language)
        serializer = GeneralReviewSerializer(queryset, context={'language': language})

        cache.set(cache_key, serializer.data, 60 * 60)

        return serializer.data

    def get_serializer_context(self) -> dict:
        return {
            'rco': RegionCalculationsObject(self.region),
            'region': self.region,
            'currency_rate': get_currency_rate(self.region),
            'striketrough_promo': strikethrough_promo(self.region),
            **super().get_serializer_context(),
        }


class ProductPageJettyAPIView(ProductDetailPageAPIView):
    model_class = Jetty
    serializer_class = JettyPDPSerializer


class SottyMixin:
    model_class = Sotty

    def get_object(self) -> Sotty:
        """Fallback to default material if UK and corduroy is selected."""
        obj = super().get_object()
        limitation_service = LimitationService(region=self.region)
        if (
            not limitation_service.is_corduroy_available
            and obj.fabric == Sotty.Fabric.CORDUROY
        ):
            obj.set_material(Sofa01Color.get_fallback(), with_save=False)
        return obj


class ProductPageSottyAPIView(SottyMixin, ProductDetailPageAPIView):
    serializer_class = SottyPDPSerializer


class ProductPageSottySingleModuleAPIView(ProductPageSottyAPIView):
    serializer_class = SottySingleModulePDPSerializer


class SottyConfiguratorPreloadAPIView(SottyMixin, ProductPageBaseAPIView):
    serializer_class = SottyPreloadSerializer

    def get_serializer_context(self) -> dict:
        return {
            'rco': RegionCalculationsObject(self.region),
            'region': self.region,
            'currency_rate': get_currency_rate(self.region),
            'striketrough_promo': strikethrough_promo(self.region),
            **super().get_serializer_context(),
        }


class ProductPageWattyAPIView(ProductDetailPageAPIView):
    model_class = Watty
    serializer_class = WattyPDPSerializer
