import json

from django.utils import timezone

import pytest

from freezegun import freeze_time

from admin_customization.anomaly_checks import InvoiceNumbering
from invoice.choices import InvoiceStatus
from invoice.encoder import TypedJSONEncoder


@pytest.fixture
def invoice_with_delivery_address_factory(invoice_factory):
    def factory(cached_to_dict_country='germany', **kwargs):
        invoice = invoice_factory.create(**kwargs)
        invoice.cached_to_dict = json.dumps(
            {'delivery_address': {'country': cached_to_dict_country}},
            cls=TypedJSONEncoder,
        )
        invoice.save(update_fields=['cached_to_dict'])
        return invoice

    return factory


@pytest.mark.django_db
class TestInvoiceNumbering:
    @freeze_time('2024-03-26 12:00:00')
    def test_check_should_return_empty_when_no_invoices(self):
        anomaly_checker = InvoiceNumbering()
        anomalies = anomaly_checker.check()
        assert anomalies == {}

    @freeze_time('2024-03-26 12:00:00')
    def test_check_should_return_empty_when_invoice_country_diff_than_order(
        self,
        invoice_with_delivery_address_factory,
        order_factory,
    ):

        order = order_factory(country='germany')
        invoice_with_delivery_address_factory(
            cached_to_dict_country='germany',
            status=InvoiceStatus.ENABLED,
            issued_at=timezone.now(),
            order=order,
            pretty_id='0001/03/2024/183875625/DE',
        )
        invoice_second = invoice_with_delivery_address_factory(
            cached_to_dict_country='austria',
            status=InvoiceStatus.ENABLED,
            issued_at=timezone.now(),
            order=order,
            pretty_id='0012/03/2024/183875625/AT',
        )

        invoice_second.save(update_fields=['cached_to_dict'])
        anomaly_checker = InvoiceNumbering()
        anomalies = anomaly_checker.check()
        assert anomalies == {}

    @freeze_time('2024-03-26 12:00:00')
    def test_check_should_return_empty_when_proper_invoice_sequence_normal(
        self,
        invoice_with_delivery_address_factory,
    ):
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.ENABLED,
            issued_at=timezone.now(),
            pretty_id='0001/03/2024/183875625/DE',
        )
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.ENABLED,
            issued_at=timezone.now(),
            pretty_id='0002/03/2024/183875625/DE',
        )
        anomaly_checker = InvoiceNumbering()
        anomalies = anomaly_checker.check()
        assert anomalies == {}

    @freeze_time('2024-03-26 12:00:00')
    def test_check_should_return_empty_when_proper_invoice_sequence_corrections(
        self,
        invoice_with_delivery_address_factory,
    ):
        de_normal_source = invoice_with_delivery_address_factory(
            status=InvoiceStatus.ENABLED,
            issued_at=timezone.now(),
            order__country='germany',
            pretty_id='00001/03/2024/183875625/DE',
        )
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.CORRECTING,
            corrected_invoice=de_normal_source,
            issued_at=timezone.now(),
            order__country='germany',
            pretty_id='FKS0001/03/2024/183875625/DE',
        )
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.CORRECTING,
            corrected_invoice=de_normal_source,
            issued_at=timezone.now(),
            order__country='germany',
            pretty_id='FKS0002/03/2024/183875625/DE',
        )
        anomaly_checker = InvoiceNumbering()
        anomalies = anomaly_checker.check()
        assert anomalies == {}

    @freeze_time('2024-03-26 12:00:00')
    def test_check_should_return_anomalies_when_gap_invoice_sequences_normal(
        self, invoice_with_delivery_address_factory
    ):
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.ENABLED,
            issued_at=timezone.now(),
            order__country='germany',
            pretty_id='00001/03/2024/183875625/DE',
        )
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.ENABLED,
            issued_at=timezone.now(),
            order__country='germany',
            pretty_id='00005/03/2024/183875625/DE',
        )
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.ENABLED,
            issued_at=timezone.now(),
            order__country='germany',
            pretty_id='00007/03/2024/183875625/DE',
        )
        anomaly_checker = InvoiceNumbering()
        anomalies = anomaly_checker.check()
        expected_anomalies = {
            'normal': [
                (3, '00001/03/2024/183875625/DE', '00005/03/2024/183875625/DE'),
                (1, '00005/03/2024/183875625/DE', '00007/03/2024/183875625/DE'),
            ]
        }

        assert anomalies == expected_anomalies

    @freeze_time('2024-03-26 12:00:00')
    def test_check_should_return_anomalies_when_gap_invoice_sequences_corrections(
        self, invoice_with_delivery_address_factory
    ):
        de_normal_source = invoice_with_delivery_address_factory(
            status=InvoiceStatus.ENABLED,
            issued_at=timezone.now(),
            order__country='germany',
            pretty_id='00001/03/2024/183875625/DE',
        )

        invoice_with_delivery_address_factory(
            status=InvoiceStatus.CORRECTING,
            order__country='germany',
            issued_at=timezone.now(),
            corrected_invoice=de_normal_source,
            pretty_id='FKS0001/03/2024/183875625/DE',
        )
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.CORRECTING,
            order__country='germany',
            issued_at=timezone.now(),
            corrected_invoice=de_normal_source,
            pretty_id='FKS0003/03/2024/183875625/DE',
        )
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.CORRECTING,
            order__country='germany',
            issued_at=timezone.now(),
            corrected_invoice=de_normal_source,
            pretty_id='FKS0008/03/2024/183875625/DE',
        )
        anomaly_checker = InvoiceNumbering()
        anomalies = anomaly_checker.check()
        expected_anomalies = {
            'normal_fks': [
                (1, 'FKS0001/03/2024/183875625/DE', 'FKS0003/03/2024/183875625/DE'),
                (4, 'FKS0003/03/2024/183875625/DE', 'FKS0008/03/2024/183875625/DE'),
            ]
        }
        assert anomalies == expected_anomalies

    @freeze_time('2024-03-26 12:00:00')
    def test_check_should_return_anomalies_when_gap_invoice_sequences_normal_region_vat(
        self, region_factory, invoice_with_delivery_address_factory, country_factory
    ):
        germany = country_factory(region_vat=True, name='germany')
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.ENABLED,
            pretty_id='RV/00001/03/2024/183875625/DE',
            issued_at=timezone.now(),
            order__country=germany.name,
            order__region=region_factory(germany=True),
        )
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.ENABLED,
            pretty_id='RV/00004/03/2024/183875625/DE',
            issued_at=timezone.now(),
            order__country=germany.name,
            order__region=region_factory(germany=True),
        )
        anomaly_checker = InvoiceNumbering()
        anomalies = anomaly_checker.check()
        expected_anomalies = {
            'germany': [
                (2, 'RV/00001/03/2024/183875625/DE', 'RV/00004/03/2024/183875625/DE')
            ]
        }
        assert anomalies == expected_anomalies

    @freeze_time('2024-03-26 12:00:00')
    def test_check_should_return_anomalies_when_gap_invoice_sequences_corrections_region_vat(
        self, region_factory, invoice_with_delivery_address_factory, country_factory
    ):
        germany = country_factory(region_vat=True, name='germany')
        de_normal_source = invoice_with_delivery_address_factory(
            status=InvoiceStatus.ENABLED,
            pretty_id='RV/00001/03/2024/183875625/DE',
            issued_at=timezone.now(),
            order__country=germany.name,
            order__region=region_factory(germany=True),
        )

        invoice_with_delivery_address_factory(
            status=InvoiceStatus.CORRECTING,
            corrected_invoice=de_normal_source,
            pretty_id='RV/FKS0001/03/2024/183875625/DE',
            issued_at=timezone.now(),
            order__country=germany.name,
            order__region=region_factory(germany=True),
        )
        invoice_with_delivery_address_factory(
            status=InvoiceStatus.CORRECTING,
            corrected_invoice=de_normal_source,
            pretty_id='RV/FKS0003/03/2024/183875625/DE',
            issued_at=timezone.now(),
            order__country=germany.name,
            order__region=region_factory(germany=True),
        )
        anomaly_checker = InvoiceNumbering()
        anomalies = anomaly_checker.check()
        expected_anomalies = {
            'Correction germany': [
                (
                    1,
                    'RV/FKS0001/03/2024/183875625/DE',
                    'RV/FKS0003/03/2024/183875625/DE',
                )
            ]
        }
        assert anomalies == expected_anomalies
